<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/excel-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Learn Excel skills through interactive challenges and gamified learning. Master Excel from basics to advanced data analysis." />
    <meta name="keywords" content="Excel, learning, tutorial, spreadsheet, data analysis, formulas, functions" />
    <title>Excel Learning Platform - Master Excel Skills</title>
    <!-- Load jQuery and required plugins before any other scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-mousewheel/3.1.13/jquery.mousewheel.min.js"></script>
    <script>
      // Make jQuery available globally
      window.jQuery = window.$ = jQuery;
      
      // Initialize jQuery plugins
      $(document).ready(function() {
        // Ensure mousewheel plugin is loaded
        if (!$.fn.mousewheel) {
          console.error('jQuery Mousewheel plugin failed to load');
        }
      });
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
