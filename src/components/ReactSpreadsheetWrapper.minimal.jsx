import React, { useEffect, useRef, useState } from 'react';
import { Univer, UniverInstanceType } from '@univerjs/core';
import { UniverSheetsPlugin } from '@univerjs/sheets';
import { UniverSheetsUIPlugin } from '@univerjs/sheets-ui';
import { UniverUIPlugin } from '@univerjs/ui';
import { UniverRenderEnginePlugin } from '@univerjs/engine-render';

// Import only essential CSS
import '@univerjs/design/lib/index.css';
import '@univerjs/ui/lib/index.css';
import '@univerjs/sheets-ui/lib/index.css';

const ReactSpreadsheetWrapper = ({ onCellChange, onCellSelect, initialData = {}, readOnly = false }) => {
  const containerRef = useRef(null);
  const univerRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);
  const [status, setStatus] = useState('Mounting...');

  const cleanupUniver = () => {
    try {
      if (univerRef.current) {
        univerRef.current.dispose();
        univerRef.current = null;
      }
      setIsInitialized(false);
    } catch (e) {
      console.warn('Error during Univer cleanup:', e);
    }
  };

  useEffect(() => {
    let isMounted = true;

    const initUniver = async () => {
      if (!isMounted || !containerRef.current) {
        setStatus('No container available');
        console.log('Univer init skipped - not mounted or no container');
        return;
      }

      // Clean up any existing instance
      cleanupUniver();

      try {
        setStatus('Initializing Univer...');
        console.log('Initializing minimal Univer...');
        console.log('Container element:', containerRef.current);
        console.log('Container dimensions:', {
          width: containerRef.current.offsetWidth,
          height: containerRef.current.offsetHeight
        });
        
        // Create minimal workbook data
        const workbookData = {
          id: 'workbook-01',
          name: 'Workbook',
          sheetOrder: ['sheet-01'],
          sheets: {
            'sheet-01': {
              id: 'sheet-01',
              name: 'Sheet1',
              cellData: {
                0: {
                  0: { v: 'A1' },
                  1: { v: 'B1' },
                  2: { v: 'C1' }
                },
                1: {
                  0: { v: 'A2' },
                  1: { v: 'B2' },
                  2: { v: 'C2' }
                }
              },
              rowCount: 100,
              columnCount: 26,
            }
          }
        };

        setStatus('Creating Univer instance...');
        console.log('Creating Univer instance...');

        // Create Univer instance
        const univer = new Univer({
          theme: undefined,
          locale: 'en-US',
          logLevel: 'warn'
        });

        setStatus('Registering plugins...');
        console.log('Registering plugins...');

        // Register only essential plugins
        univer.registerPlugin(UniverRenderEnginePlugin);
        univer.registerPlugin(UniverUIPlugin, {
          container: containerRef.current,
          header: true,
          toolbar: true,
          footer: true
        });
        univer.registerPlugin(UniverSheetsPlugin);
        univer.registerPlugin(UniverSheetsUIPlugin);

        setStatus('Creating workbook...');
        console.log('Creating workbook...');

        // Create workbook
        const workbook = univer.createUnit(UniverInstanceType.UNIVER_SHEET, workbookData);

        console.log('Univer initialized successfully:', workbook);

        // Store references
        univerRef.current = univer;
        setStatus('Ready');
        setIsInitialized(true);

      } catch (error) {
        console.error('Error initializing Univer:', error);
        setError(error.message);
      }
    };

    // Add a small delay to ensure DOM is ready
    const timer = setTimeout(() => {
      initUniver();
    }, 100);

    // Cleanup function
    return () => {
      isMounted = false;
      clearTimeout(timer);
      cleanupUniver();
    };
  }, []);

  if (error) {
    return (
      <div className="w-full h-full bg-red-50 border-2 border-red-200 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-2">Spreadsheet Error</p>
          <p className="text-sm text-red-500">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full" style={{ minHeight: '400px', position: 'relative' }}>
      <div
        ref={containerRef}
        style={{
          width: '100%',
          height: '100%',
          minHeight: '400px',
          position: 'relative',
          border: '1px solid #e5e7eb',
          borderRadius: '8px',
          overflow: 'hidden'
        }}
      />
      {!isInitialized && !error && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(249, 250, 251, 0.9)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10
          }}
        >
          <div style={{ textAlign: 'center' }}>
            <div
              style={{
                width: '32px',
                height: '32px',
                border: '2px solid #e5e7eb',
                borderTop: '2px solid #3b82f6',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                margin: '0 auto 8px'
              }}
            ></div>
            <p style={{ color: '#6b7280', fontSize: '14px' }}>{status}</p>
          </div>
        </div>
      )}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default ReactSpreadsheetWrapper;
