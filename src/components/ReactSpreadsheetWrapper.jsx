import React, { useEffect, useRef, useState } from 'react';
import { Univer, UniverInstanceType } from '@univerjs/core';
import { UniverSheetsPlugin } from '@univerjs/sheets';
import { UniverSheetsUIPlugin } from '@univerjs/sheets-ui';
import { UniverUIPlugin } from '@univerjs/ui';
import { UniverRenderEnginePlugin } from '@univerjs/engine-render';
import { UniverFormulaEnginePlugin } from '@univerjs/engine-formula';
import { UniverSheetsFormulaPlugin } from '@univerjs/sheets-formula';
import { UniverSheetsFormulaUIPlugin } from '@univerjs/sheets-formula-ui';
import { UniverSheetsNumfmtPlugin } from '@univerjs/sheets-numfmt';
import { UniverSheetsNumfmtUIPlugin } from '@univerjs/sheets-numfmt-ui';
import { UniverDesignPlugin } from '@univerjs/design';

// Import Univer CSS
import '@univerjs/design/lib/index.css';
import '@univerjs/ui/lib/index.css';
import '@univerjs/sheets-ui/lib/index.css';
import '@univerjs/sheets-formula-ui/lib/index.css';
import '@univerjs/sheets-numfmt-ui/lib/index.css';

const transformToUniverData = (inputData) => {
  const cellData = {};

  if (inputData && typeof inputData === 'object') {
    // Process regular cell data
    for (const key in inputData) {
      if (key === 'currentCell' || key === 'navigatedCell') continue;

      // Check for cell data (e.g., 'A1', 'B2')
      const cellMatch = key.match(/^([A-Z]+)([1-9]\d*)$/);
      if (cellMatch) {
        const colStr = cellMatch[1];
        const rowNum = parseInt(cellMatch[2], 10);
        let colIndex = 0;

        // Convert column letters to 0-based index (A=0, B=1, ..., Z=25, AA=26, etc.)
        for (let i = 0; i < colStr.length; i++) {
          colIndex = colIndex * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
        }
        colIndex -= 1; // Convert to 0-based index

        const rowIndex = rowNum - 1; // Convert to 0-based index

        // Initialize row if it doesn't exist
        if (!cellData[rowIndex]) {
          cellData[rowIndex] = {};
        }

        // Create cell object with value
        const cellObj = { v: inputData[key] };

        // Check for formatting data
        const formatKey = `${key}_format`;
        if (inputData[formatKey] && typeof inputData[formatKey] === 'object') {
          Object.assign(cellObj, inputData[formatKey]);
        }

        cellData[rowIndex][colIndex] = cellObj;
      }
    }
  }

  return {
    id: 'workbook-01',
    name: 'Workbook',
    sheetOrder: ['sheet-01'],
    sheets: {
      'sheet-01': {
        id: 'sheet-01',
        name: 'Sheet1',
        cellData,
        rowCount: 1000,
        columnCount: 26,
      }
    }
  };
};

const ReactSpreadsheetWrapper = ({ onCellChange, onCellSelect, initialData = {}, readOnly = false }) => {
  const luckysheetId = 'luckysheet-container';
  const containerRef = useRef(null);

  const cleanupLuckysheet = () => {
    try {
      if (typeof luckysheet.getLuckysheetfile === 'function') {
        const sheetFile = luckysheet.getLuckysheetfile();
        if (sheetFile && Array.isArray(sheetFile) && sheetFile.length > 0) {
          luckysheet.destroy();
        }
      }
    } catch (e) {
      console.warn('Error during Luckysheet cleanup:', e);
    }
  };

  // Convert cell coordinates to Excel reference (e.g., {r:0,c:2} -> 'C1')
  const getCellRef = (r, c) => {
    let col = '';
    let colNum = c + 1; // Convert to 1-based column
    
    while (colNum > 0) {
      const remainder = (colNum - 1) % 26;
      col = String.fromCharCode(65 + remainder) + col;
      colNum = Math.floor((colNum - 1) / 26);
    }
    
    return col + (r + 1);
  };

  // Handle cell changes
  const handleCellChange = (cell, value, oldValue) => {
    if (!cell || !onCellChange) return;
    
    // Get cell reference
    const cellRef = getCellRef(cell.r || 0, cell.c || 0);
    
    // Create a cell object with the reference and value
    const cellData = {
      r: cell.r,
      c: cell.c,
      v: value,
      ref: cellRef,
      format: cell.format
    };
    
    console.log('Cell changed:', cellData);
    onCellChange(cellData, value, oldValue);
  };

  // Handle cell selection
  const handleCellSelect = (cell) => {
    if (!cell || !onCellSelect) return;
    
    // Get cell reference
    const cellRef = getCellRef(cell.r || 0, cell.c || 0);
    
    // Create a cell object with the reference
    const cellData = {
      r: cell.r,
      c: cell.c,
      ref: cellRef,
      format: cell.format
    };
    
    console.log('Cell selected:', cellData);
    onCellSelect(cellData);
  };
  
  // Add event listener for cell selection changes
  useEffect(() => {
    const handleSelectionChange = () => {
      try {
        const selection = luckysheet.getRange();
        if (selection && selection.length > 0) {
          const { row, column } = selection[0];
          if (row && column) {
            handleCellSelect({ r: row[0], c: column[0] });
          }
        }
      } catch (e) {
        console.warn('Error getting selection:', e);
      }
    };
    
    // Add event listener for selection changes
    document.addEventListener('luckysheet_selectChange', handleSelectionChange);
    
    return () => {
      document.removeEventListener('luckysheet_selectChange', handleSelectionChange);
    };
  }, [onCellSelect]);

  useEffect(() => {
    let isMounted = true;
    let timer;

    const initLuckysheet = () => {
      if (!isMounted) return;
      
      try {
        if (!window.$ || !window.$.fn || !window.$.fn.mousewheel) {
          console.error('Required JavaScript libraries not loaded');
          return;
        }

        // Clean up any existing instance
        cleanupLuckysheet();

        // Prepare data
        const sheetData = transformToLuckysheetData(initialData || {});
        
        // Initialize Luckysheet with options
        const options = {
          container: luckysheetId,
          title: 'Spreadsheet',
          lang: 'en',
          plugins: [], // Disable plugins for now
          showinfobar: false,
          showsheetbar: false,
          showtoolbar: true,
          showstatisticBar: true,
          data: sheetData,
          allowCopy: !readOnly,
          allowEdit: !readOnly,
          enableAddRow: !readOnly,
          enableAddCol: !readOnly,
          enablePage: false,
          row: {
            len: 100,
            height: 25,
          },
          column: {
            len: 50,
            width: 100,
          },
          hook: {
            cellUpdated: (cell, value, oldValue) => {
              if (isMounted && cell) {
                // Add a small delay to ensure the cell is fully updated
                setTimeout(() => {
                  handleCellChange(cell, value, oldValue);
                }, 50);
              }
            },
            cellClickBefore: (e, cell) => {
              if (isMounted && cell) {
                handleCellSelect(cell);
              }
              return !readOnly; // Allow selection but not editing in read-only mode
            },
            // Add selection change hook to track cell selection
            onTogglePivotTable: (cell) => {
              if (isMounted && cell) {
                handleCellSelect(cell);
              }
            },
            // Add cell selection change hook
            cellSelectBefore: (cell) => {
              if (isMounted && cell) {
                handleCellSelect(cell);
              }
              return true;
            },
            // Add range selection hook
            rangeSelect: (cell) => {
              if (isMounted && cell) {
                handleCellSelect(cell);
              }
              return true;
            }
          }
        };

        // Initialize Luckysheet
        if (document.getElementById(luckysheetId)) {
          luckysheet.create(options);
        }
      } catch (error) {
        console.error('Error initializing Luckysheet:', error);
      }
    };

    // Initialize with a small delay to ensure DOM is ready
    timer = setTimeout(initLuckysheet, 100);

    // Cleanup function
    return () => {
      isMounted = false;
      clearTimeout(timer);
      cleanupLuckysheet();
    };
  }, [initialData, onCellChange, onCellSelect, readOnly, luckysheetId]);

  return (
    <div 
      id={luckysheetId}
      ref={containerRef}
      style={{
        width: '100%',
        height: '500px',
        margin: '0 auto',
        position: 'relative'
      }}
    />
  );
};

export default ReactSpreadsheetWrapper;
