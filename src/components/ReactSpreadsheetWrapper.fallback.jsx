import React, { useState, useEffect } from 'react';
import {
  Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight,
  Palette, Type, Save, Undo, Redo, Copy, Scissors,
  Plus, Minus, X, Divide, Percent, DollarSign, Hash,
  Grid3X3, Square, Filter, ArrowUp, ArrowDown, Clipboard
} from 'lucide-react';

const ReactSpreadsheetWrapper = ({ onCellChange, onCellSelect, initialData = {}, readOnly = false }) => {
  const [selectedCell, setSelectedCell] = useState('A1');
  const [cellData, setCellData] = useState({
    A1: '', B1: '', C1: '', D1: '', E1: '',
    A2: '', B2: '', C2: '', D2: '', E2: '',
    A3: '', B3: '', C3: '', D3: '', E3: '',
    A4: '', B4: '', C4: '', D4: '', E4: '',
    A5: '', B5: '', C5: '', D5: '', E5: '',
    ...initialData
  });

  // Formatting state
  const [cellFormatting, setCellFormatting] = useState({});
  const [formulaBarValue, setFormulaBarValue] = useState('');
  const [isEditingFormula, setIsEditingFormula] = useState(false);

  // Column and row sizing
  const [columnWidths, setColumnWidths] = useState({});
  const [rowHeights, setRowHeights] = useState({});
  const [isResizing, setIsResizing] = useState(false);
  const [resizeTarget, setResizeTarget] = useState(null);

  // Generate grid cells
  const rows = 10;
  const cols = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];

  // Update formula bar when selected cell changes
  useEffect(() => {
    setFormulaBarValue(cellData[selectedCell] || '');
  }, [selectedCell, cellData]);

  const handleCellClick = (cellRef) => {
    setSelectedCell(cellRef);
    setFormulaBarValue(cellData[cellRef] || '');
    setIsEditingFormula(false);
    if (onCellSelect) {
      onCellSelect(cellRef);
    }
  };

  const handleCellChange = (cellRef, value) => {
    const newData = { ...cellData, [cellRef]: value };
    setCellData(newData);
    setFormulaBarValue(value);
    if (onCellChange) {
      onCellChange(newData);
    }
  };

  // Formatting functions
  const applyFormatting = (formatType, value = true) => {
    const currentFormat = cellFormatting[selectedCell] || {};
    const newFormat = { ...currentFormat, [formatType]: value };
    setCellFormatting({
      ...cellFormatting,
      [selectedCell]: newFormat
    });
  };

  const handleFormulaBarChange = (value) => {
    setFormulaBarValue(value);
    if (!isEditingFormula) {
      setIsEditingFormula(true);
    }
  };

  const evaluateFormula = (formula, data) => {
    if (!formula.startsWith('=')) {
      return formula;
    }

    try {
      // Simple formula evaluation for basic operations
      let expression = formula.substring(1); // Remove the '=' sign

      // Replace cell references with their values
      const cellRefPattern = /([A-Z]+)([1-9]\d*)/g;
      expression = expression.replace(cellRefPattern, (match, col, row) => {
        const cellRef = col + row;
        const value = data[cellRef] || '0';
        return isNaN(value) ? '0' : value;
      });

      // Evaluate simple mathematical expressions
      // Only allow basic operations for security
      if (/^[\d+\-*/().\s]+$/.test(expression)) {
        const result = Function('"use strict"; return (' + expression + ')')();
        return isNaN(result) ? '#ERROR!' : result.toString();
      }

      return '#ERROR!';
    } catch (error) {
      return '#ERROR!';
    }
  };

  const handleFormulaBarSubmit = () => {
    let valueToSet = formulaBarValue;

    // If it's a formula, evaluate it
    if (formulaBarValue.startsWith('=')) {
      const result = evaluateFormula(formulaBarValue, cellData);
      // Store both the formula and the calculated result
      const newData = {
        ...cellData,
        [selectedCell]: formulaBarValue,
        [`${selectedCell}_calculated`]: result
      };
      setCellData(newData);
      if (onCellChange) {
        onCellChange(newData);
      }
    } else {
      handleCellChange(selectedCell, formulaBarValue);
    }

    setIsEditingFormula(false);
  };

  const getCellStyle = (cellRef) => {
    const format = cellFormatting[cellRef] || {};
    return {
      fontWeight: format.bold ? 'bold' : 'normal',
      fontStyle: format.italic ? 'italic' : 'normal',
      textDecoration: format.underline ? 'underline' : 'none',
      textAlign: format.align || 'left',
      backgroundColor: format.backgroundColor || 'transparent',
      color: format.color || '#000000'
    };
  };

  // Resize handlers
  const getColumnWidth = (col) => {
    return columnWidths[col] || 80; // Default width
  };

  const getRowHeight = (row) => {
    return rowHeights[row] || 32; // Default height
  };

  const handleColumnResize = (col, newWidth) => {
    setColumnWidths(prev => ({
      ...prev,
      [col]: Math.max(50, newWidth) // Minimum width of 50px
    }));
  };

  const handleRowResize = (row, newHeight) => {
    setRowHeights(prev => ({
      ...prev,
      [row]: Math.max(20, newHeight) // Minimum height of 20px
    }));
  };

  const handleKeyDown = (e, cellRef) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      // Move to next row
      const col = cellRef[0];
      const row = parseInt(cellRef.slice(1));
      const nextCell = `${col}${row + 1}`;
      if (row < rows) {
        handleCellClick(nextCell);
      }
    }
  };

  return (
    <div className="w-full h-full bg-white border border-gray-300 rounded-lg overflow-hidden flex flex-col">
      {/* Ribbon Menu */}
      <div className="bg-gray-50 border-b border-gray-300 p-2">
        <div className="flex items-center space-x-3 mb-2 overflow-x-auto">
          {/* File Operations */}
          <div className="flex items-center space-x-1">
            <button className="p-1 hover:bg-gray-200 rounded" title="Undo">
              <Undo className="w-4 h-4" />
            </button>
            <button className="p-1 hover:bg-gray-200 rounded" title="Redo">
              <Redo className="w-4 h-4" />
            </button>
          </div>

          <div className="w-px h-6 bg-gray-300"></div>

          {/* Clipboard */}
          <div className="flex items-center space-x-1">
            <button className="p-1 hover:bg-gray-200 rounded" title="Cut">
              <Scissors className="w-4 h-4" />
            </button>
            <button className="p-1 hover:bg-gray-200 rounded" title="Copy">
              <Copy className="w-4 h-4" />
            </button>
            <button className="p-1 hover:bg-gray-200 rounded" title="Paste">
              <Clipboard className="w-4 h-4" />
            </button>
          </div>

          <div className="w-px h-6 bg-gray-300"></div>

          {/* Text Formatting */}
          <div className="flex items-center space-x-1">
            <button
              className={`p-1 hover:bg-gray-200 rounded ${cellFormatting[selectedCell]?.bold ? 'bg-blue-200' : ''}`}
              onClick={() => applyFormatting('bold', !cellFormatting[selectedCell]?.bold)}
              title="Bold"
            >
              <Bold className="w-4 h-4" />
            </button>
            <button
              className={`p-1 hover:bg-gray-200 rounded ${cellFormatting[selectedCell]?.italic ? 'bg-blue-200' : ''}`}
              onClick={() => applyFormatting('italic', !cellFormatting[selectedCell]?.italic)}
              title="Italic"
            >
              <Italic className="w-4 h-4" />
            </button>
            <button
              className={`p-1 hover:bg-gray-200 rounded ${cellFormatting[selectedCell]?.underline ? 'bg-blue-200' : ''}`}
              onClick={() => applyFormatting('underline', !cellFormatting[selectedCell]?.underline)}
              title="Underline"
            >
              <Underline className="w-4 h-4" />
            </button>
          </div>

          <div className="w-px h-6 bg-gray-300"></div>

          {/* Alignment */}
          <div className="flex items-center space-x-1">
            <button
              className="p-1 hover:bg-gray-200 rounded"
              onClick={() => applyFormatting('align', 'left')}
              title="Align Left"
            >
              <AlignLeft className="w-4 h-4" />
            </button>
            <button
              className="p-1 hover:bg-gray-200 rounded"
              onClick={() => applyFormatting('align', 'center')}
              title="Align Center"
            >
              <AlignCenter className="w-4 h-4" />
            </button>
            <button
              className="p-1 hover:bg-gray-200 rounded"
              onClick={() => applyFormatting('align', 'right')}
              title="Align Right"
            >
              <AlignRight className="w-4 h-4" />
            </button>
          </div>

          <div className="w-px h-6 bg-gray-300"></div>

          {/* Colors */}
          <div className="flex items-center space-x-1">
            <button
              className="p-1 hover:bg-gray-200 rounded"
              onClick={() => applyFormatting('backgroundColor', '#ffff00')}
              title="Yellow Background"
            >
              <div className="w-4 h-4 bg-yellow-400 border border-gray-300 rounded"></div>
            </button>
            <button
              className="p-1 hover:bg-gray-200 rounded"
              onClick={() => applyFormatting('backgroundColor', '#90EE90')}
              title="Green Background"
            >
              <div className="w-4 h-4 bg-green-300 border border-gray-300 rounded"></div>
            </button>
            <button
              className="p-1 hover:bg-gray-200 rounded"
              onClick={() => applyFormatting('backgroundColor', '#FFB6C1')}
              title="Pink Background"
            >
              <div className="w-4 h-4 bg-pink-300 border border-gray-300 rounded"></div>
            </button>
          </div>

          <div className="w-px h-6 bg-gray-300"></div>

          {/* Number Format */}
          <div className="flex items-center space-x-1">
            <button className="p-1 hover:bg-gray-200 rounded" title="Currency">
              <DollarSign className="w-4 h-4" />
            </button>
            <button className="p-1 hover:bg-gray-200 rounded" title="Percentage">
              <Percent className="w-4 h-4" />
            </button>
            <button className="p-1 hover:bg-gray-200 rounded" title="Number">
              <Hash className="w-4 h-4" />
            </button>
          </div>

          <div className="w-px h-6 bg-gray-300"></div>

          {/* Borders */}
          <div className="flex items-center space-x-1">
            <button className="p-1 hover:bg-gray-200 rounded" title="Grid Borders">
              <Grid3X3 className="w-4 h-4" />
            </button>
            <button className="p-1 hover:bg-gray-200 rounded" title="No Borders">
              <Square className="w-4 h-4" />
            </button>
          </div>

          <div className="w-px h-6 bg-gray-300"></div>

          {/* Data Tools */}
          <div className="flex items-center space-x-1">
            <button className="p-1 hover:bg-gray-200 rounded" title="Sort Ascending">
              <ArrowUp className="w-4 h-4" />
            </button>
            <button className="p-1 hover:bg-gray-200 rounded" title="Sort Descending">
              <ArrowDown className="w-4 h-4" />
            </button>
            <button className="p-1 hover:bg-gray-200 rounded" title="Filter">
              <Filter className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Formula Bar */}
        <div className="flex items-center space-x-2">
          <div className="text-sm font-medium text-gray-700 w-12">{selectedCell}</div>
          <input
            type="text"
            value={formulaBarValue}
            onChange={(e) => handleFormulaBarChange(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleFormulaBarSubmit();
              }
            }}
            onBlur={handleFormulaBarSubmit}
            className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
            placeholder="Enter formula or value..."
          />
        </div>
      </div>

      {/* Spreadsheet Grid */}
      <div className="flex-1 overflow-auto">
        <table className="border-collapse">
          <thead>
            <tr>
              <th
                className="bg-gray-100 border border-gray-300 text-xs font-medium text-gray-600 relative"
                style={{ width: '40px', height: '32px' }}
              ></th>
              {cols.map(col => (
                <th
                  key={col}
                  className="bg-gray-100 border border-gray-300 text-xs font-medium text-gray-600 relative group"
                  style={{ width: `${getColumnWidth(col)}px`, height: '32px' }}
                >
                  <div className="flex items-center justify-center h-full">
                    {col}
                  </div>
                  {/* Column resize handle */}
                  <div
                    className="absolute right-0 top-0 w-1 h-full cursor-col-resize bg-transparent hover:bg-blue-400 opacity-0 group-hover:opacity-100"
                    onMouseDown={(e) => {
                      e.preventDefault();
                      const startX = e.clientX;
                      const startWidth = getColumnWidth(col);

                      const handleMouseMove = (e) => {
                        const newWidth = startWidth + (e.clientX - startX);
                        handleColumnResize(col, newWidth);
                      };

                      const handleMouseUp = () => {
                        document.removeEventListener('mousemove', handleMouseMove);
                        document.removeEventListener('mouseup', handleMouseUp);
                      };

                      document.addEventListener('mousemove', handleMouseMove);
                      document.addEventListener('mouseup', handleMouseUp);
                    }}
                  />
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {Array.from({ length: rows }, (_, rowIndex) => {
              const rowNum = rowIndex + 1;
              return (
                <tr key={rowNum} style={{ height: `${getRowHeight(rowNum)}px` }}>
                  <td
                    className="bg-gray-100 border border-gray-300 text-xs font-medium text-gray-600 text-center relative group"
                    style={{ width: '40px', height: `${getRowHeight(rowNum)}px` }}
                  >
                    <div className="flex items-center justify-center h-full">
                      {rowNum}
                    </div>
                    {/* Row resize handle */}
                    <div
                      className="absolute bottom-0 left-0 w-full h-1 cursor-row-resize bg-transparent hover:bg-blue-400 opacity-0 group-hover:opacity-100"
                      onMouseDown={(e) => {
                        e.preventDefault();
                        const startY = e.clientY;
                        const startHeight = getRowHeight(rowNum);

                        const handleMouseMove = (e) => {
                          const newHeight = startHeight + (e.clientY - startY);
                          handleRowResize(rowNum, newHeight);
                        };

                        const handleMouseUp = () => {
                          document.removeEventListener('mousemove', handleMouseMove);
                          document.removeEventListener('mouseup', handleMouseUp);
                        };

                        document.addEventListener('mousemove', handleMouseMove);
                        document.addEventListener('mouseup', handleMouseUp);
                      }}
                    />
                  </td>
                  {cols.map(col => {
                    const cellRef = `${col}${rowNum}`;
                    const isSelected = selectedCell === cellRef;
                    return (
                      <td
                        key={cellRef}
                        className={`border border-gray-300 p-0 ${
                          isSelected ? 'bg-blue-100 border-blue-500' : 'bg-white hover:bg-gray-50'
                        }`}
                        style={{
                          width: `${getColumnWidth(col)}px`,
                          height: `${getRowHeight(rowNum)}px`
                        }}
                        onClick={() => handleCellClick(cellRef)}
                      >
                        <input
                          type="text"
                          value={
                            cellData[cellRef]?.startsWith('=') && cellData[`${cellRef}_calculated`]
                              ? cellData[`${cellRef}_calculated`]
                              : (cellData[cellRef] || '')
                          }
                          onChange={(e) => handleCellChange(cellRef, e.target.value)}
                          onKeyDown={(e) => handleKeyDown(e, cellRef)}
                          className={`w-full h-full px-1 text-xs border-none outline-none bg-transparent ${
                            isSelected ? 'bg-blue-50' : ''
                          }`}
                          readOnly={readOnly}
                          style={{
                            fontSize: '12px',
                            ...getCellStyle(cellRef)
                          }}
                        />
                      </td>
                    );
                  })}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>


    </div>
  );
};

export default ReactSpreadsheetWrapper;
