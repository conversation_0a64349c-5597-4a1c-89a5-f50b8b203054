import React, { useState, useEffect } from 'react';

const ReactSpreadsheetWrapper = ({ onCellChange, onCellSelect, initialData = {}, readOnly = false }) => {
  const [selectedCell, setSelectedCell] = useState('A1');
  const [cellData, setCellData] = useState({
    A1: '', B1: '', C1: '', D1: '', E1: '',
    A2: '', B2: '', C2: '', D2: '', E2: '',
    A3: '', B3: '', C3: '', D3: '', E3: '',
    A4: '', B4: '', C4: '', D4: '', E4: '',
    A5: '', B5: '', C5: '', D5: '', E5: '',
    ...initialData
  });

  // Generate grid cells
  const rows = 10;
  const cols = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];

  const handleCellClick = (cellRef) => {
    setSelectedCell(cellRef);
    if (onCellSelect) {
      onCellSelect(cellRef);
    }
  };

  const handleCellChange = (cellRef, value) => {
    const newData = { ...cellData, [cellRef]: value };
    setCellData(newData);
    if (onCellChange) {
      onCellChange(newData);
    }
  };

  const handleKeyDown = (e, cellRef) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      // Move to next row
      const col = cellRef[0];
      const row = parseInt(cellRef.slice(1));
      const nextCell = `${col}${row + 1}`;
      if (row < rows) {
        handleCellClick(nextCell);
      }
    }
  };

  return (
    <div className="w-full h-full bg-white border border-gray-300 rounded-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gray-50 border-b border-gray-300 p-2">
        <div className="text-sm font-medium text-gray-700">
          Simple Spreadsheet (Fallback Mode)
        </div>
        <div className="text-xs text-gray-500">
          Selected: {selectedCell} | Value: {cellData[selectedCell] || '(empty)'}
        </div>
      </div>

      {/* Spreadsheet Grid */}
      <div className="overflow-auto" style={{ height: 'calc(100% - 60px)' }}>
        <table className="w-full border-collapse">
          <thead>
            <tr>
              <th className="w-12 h-8 bg-gray-100 border border-gray-300 text-xs font-medium text-gray-600"></th>
              {cols.map(col => (
                <th key={col} className="w-20 h-8 bg-gray-100 border border-gray-300 text-xs font-medium text-gray-600">
                  {col}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {Array.from({ length: rows }, (_, rowIndex) => {
              const rowNum = rowIndex + 1;
              return (
                <tr key={rowNum}>
                  <td className="w-12 h-8 bg-gray-100 border border-gray-300 text-xs font-medium text-gray-600 text-center">
                    {rowNum}
                  </td>
                  {cols.map(col => {
                    const cellRef = `${col}${rowNum}`;
                    const isSelected = selectedCell === cellRef;
                    return (
                      <td
                        key={cellRef}
                        className={`w-20 h-8 border border-gray-300 p-0 ${
                          isSelected ? 'bg-blue-100 border-blue-500' : 'bg-white hover:bg-gray-50'
                        }`}
                        onClick={() => handleCellClick(cellRef)}
                      >
                        <input
                          type="text"
                          value={cellData[cellRef] || ''}
                          onChange={(e) => handleCellChange(cellRef, e.target.value)}
                          onKeyDown={(e) => handleKeyDown(e, cellRef)}
                          className={`w-full h-full px-1 text-xs border-none outline-none bg-transparent ${
                            isSelected ? 'bg-blue-50' : ''
                          }`}
                          readOnly={readOnly}
                          style={{ fontSize: '12px' }}
                        />
                      </td>
                    );
                  })}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Instructions */}
      <div className="bg-yellow-50 border-t border-yellow-200 p-2">
        <div className="text-xs text-yellow-800">
          💡 This is a simple fallback spreadsheet. Click cells to select, type to enter data.
        </div>
      </div>
    </div>
  );
};

export default ReactSpreadsheetWrapper;
