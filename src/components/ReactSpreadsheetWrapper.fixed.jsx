import React, { useEffect, useRef, useState } from 'react';
import { Univer, UniverInstanceType, ICommandService } from '@univerjs/core';
import { UniverSheetsPlugin } from '@univerjs/sheets';
import { UniverSheetsUIPlugin } from '@univerjs/sheets-ui';
import { UniverUIPlugin } from '@univerjs/ui';
import { UniverRenderEnginePlugin } from '@univerjs/engine-render';
import { UniverFormulaEnginePlugin } from '@univerjs/engine-formula';
import { UniverSheetsFormulaPlugin } from '@univerjs/sheets-formula';
import { UniverSheetsFormulaUIPlugin } from '@univerjs/sheets-formula-ui';
import { UniverSheetsNumfmtPlugin } from '@univerjs/sheets-numfmt';
import { UniverSheetsNumfmtUIPlugin } from '@univerjs/sheets-numfmt-ui';
import { UniverDocsPlugin } from '@univerjs/docs';
import { UniverDocsUIPlugin } from '@univerjs/docs-ui';

// Import Univer CSS
import '@univerjs/design/lib/index.css';
import '@univerjs/ui/lib/index.css';
import '@univerjs/sheets-ui/lib/index.css';
import '@univerjs/sheets-formula-ui/lib/index.css';
import '@univerjs/sheets-numfmt-ui/lib/index.css';

const transformToUniverData = (inputData) => {
  const cellData = {};

  if (inputData && typeof inputData === 'object') {
    // Process regular cell data
    for (const key in inputData) {
      if (key === 'currentCell' || key === 'navigatedCell') continue;

      // Check for cell data (e.g., 'A1', 'B2')
      const cellMatch = key.match(/^([A-Z]+)([1-9]\d*)$/);
      if (cellMatch) {
        const colStr = cellMatch[1];
        const rowNum = parseInt(cellMatch[2], 10);
        let colIndex = 0;

        // Convert column letters to 0-based index (A=0, B=1, ..., Z=25, AA=26, etc.)
        for (let i = 0; i < colStr.length; i++) {
          colIndex = colIndex * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
        }
        colIndex -= 1; // Convert to 0-based index

        const rowIndex = rowNum - 1; // Convert to 0-based index

        // Initialize row if it doesn't exist
        if (!cellData[rowIndex]) {
          cellData[rowIndex] = {};
        }

        // Create cell object with value
        const cellObj = { v: inputData[key] };

        // Check for formatting data
        const formatKey = `${key}_format`;
        if (inputData[formatKey] && typeof inputData[formatKey] === 'object') {
          Object.assign(cellObj, inputData[formatKey]);
        }

        cellData[rowIndex][colIndex] = cellObj;
      }
    }
  }

  return {
    id: 'workbook-01',
    name: 'Workbook',
    sheetOrder: ['sheet-01'],
    sheets: {
      'sheet-01': {
        id: 'sheet-01',
        name: 'Sheet1',
        cellData,
        rowCount: 1000,
        columnCount: 26,
      }
    }
  };
};

const ReactSpreadsheetWrapper = ({ onCellChange, onCellSelect, initialData = {}, readOnly = false }) => {
  const containerRef = useRef(null);
  const univerRef = useRef(null);
  const workbookRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Convert cell coordinates to Excel reference (e.g., {r:0,c:2} -> 'C1')
  const getCellRef = (r, c) => {
    let col = '';
    let colNum = c + 1; // Convert to 1-based column

    while (colNum > 0) {
      const remainder = (colNum - 1) % 26;
      col = String.fromCharCode(65 + remainder) + col;
      colNum = Math.floor((colNum - 1) / 26);
    }

    return col + (r + 1);
  };

  const cleanupUniver = () => {
    try {
      if (univerRef.current) {
        univerRef.current.dispose();
        univerRef.current = null;
      }
      workbookRef.current = null;
      setIsInitialized(false);
    } catch (e) {
      console.warn('Error during Univer cleanup:', e);
    }
  };

  // Handle cell changes
  const handleCellChange = (params) => {
    if (!params || !onCellChange) return;

    const { row, col, value, oldValue } = params;

    // Get cell reference
    const cellRef = getCellRef(row, col);

    // Create a cell object with the reference and value
    const cellData = {
      r: row,
      c: col,
      v: value,
      ref: cellRef
    };

    console.log('Cell changed:', cellData);
    onCellChange(cellData, value, oldValue);
  };

  // Handle cell selection
  const handleCellSelect = (params) => {
    if (!params || !onCellSelect) return;

    const { row, col } = params;

    // Get cell reference
    const cellRef = getCellRef(row, col);

    // Create a cell object with the reference
    const cellData = {
      r: row,
      c: col,
      ref: cellRef
    };

    console.log('Cell selected:', cellData);
    onCellSelect(cellData);
  };

  useEffect(() => {
    let isMounted = true;

    const initUniver = async () => {
      if (!isMounted || !containerRef.current) return;

      // Clean up any existing instance
      cleanupUniver();

      try {
        // Prepare data
        const workbookData = transformToUniverData(initialData || {});
        console.log('Initializing Univer with data:', workbookData);

        // Create Univer instance
        const univer = new Univer({
          theme: undefined, // Use default theme
          locale: 'en-US',
          logLevel: 'warn'
        });

        console.log('Univer instance created:', univer);

        // Register plugins
        console.log('Registering plugins...');
        console.log('Container element:', containerRef.current);

        univer.registerPlugin(UniverRenderEnginePlugin);
        console.log('UniverRenderEnginePlugin registered');

        univer.registerPlugin(UniverDocsPlugin);
        console.log('UniverDocsPlugin registered');

        univer.registerPlugin(UniverUIPlugin, {
          container: containerRef.current,
          header: true,
          toolbar: true,
          footer: true
        });
        console.log('UniverUIPlugin registered');

        univer.registerPlugin(UniverDocsUIPlugin);
        console.log('UniverDocsUIPlugin registered');

        univer.registerPlugin(UniverSheetsPlugin);
        console.log('UniverSheetsPlugin registered');

        univer.registerPlugin(UniverSheetsUIPlugin);
        console.log('UniverSheetsUIPlugin registered');

        univer.registerPlugin(UniverFormulaEnginePlugin);
        console.log('UniverFormulaEnginePlugin registered');

        univer.registerPlugin(UniverSheetsFormulaPlugin);
        console.log('UniverSheetsFormulaPlugin registered');

        univer.registerPlugin(UniverSheetsFormulaUIPlugin);
        console.log('UniverSheetsFormulaUIPlugin registered');

        univer.registerPlugin(UniverSheetsNumfmtPlugin);
        console.log('UniverSheetsNumfmtPlugin registered');

        univer.registerPlugin(UniverSheetsNumfmtUIPlugin);
        console.log('UniverSheetsNumfmtUIPlugin registered');

        // Create workbook
        console.log('Creating workbook with data:', workbookData);
        const workbook = univer.createUnit(UniverInstanceType.UNIVER_SHEET, workbookData);
        console.log('Workbook created:', workbook);

        // Store references
        univerRef.current = univer;
        workbookRef.current = workbook;
        console.log('References stored, setting initialized to true');
        setIsInitialized(true);

        // Set up event listeners for cell changes and selection
        // Note: Univer uses a different event system, we'll need to use the command service
        const injector = univer.__getInjector();
        const commandService = injector.get(ICommandService);

        // Listen for cell value changes
        const disposable1 = commandService.onCommandExecuted((command) => {
          if (command.id === 'sheet.command.set-range-values' && command.params) {
            const { range, value } = command.params;
            if (range && value && isMounted) {
              handleCellChange({
                row: range.startRow,
                col: range.startColumn,
                value: value.v,
                oldValue: null
              });
            }
          }
        });

        // Listen for selection changes
        const disposable2 = commandService.onCommandExecuted((command) => {
          if (command.id === 'sheet.operation.set-selections' && command.params) {
            const { selections } = command.params;
            if (selections && selections.length > 0 && isMounted) {
              const selection = selections[0];
              handleCellSelect({
                row: selection.startRow,
                col: selection.startColumn
              });
            }
          }
        });

        // Store disposables for cleanup
        univerRef.current._disposables = [disposable1, disposable2];

      } catch (error) {
        console.error('Error initializing Univer:', error);
      }
    };

    // Initialize Univer
    initUniver();

    // Cleanup function
    return () => {
      isMounted = false;
      if (univerRef.current && univerRef.current._disposables) {
        univerRef.current._disposables.forEach(d => d.dispose());
      }
      cleanupUniver();
    };
  }, [initialData, onCellChange, onCellSelect, readOnly]);

  return (
    <div
      ref={containerRef}
      style={{
        width: '100%',
        height: '500px',
        margin: '0 auto',
        position: 'relative'
      }}
    />
  );
};

export default ReactSpreadsheetWrapper;
