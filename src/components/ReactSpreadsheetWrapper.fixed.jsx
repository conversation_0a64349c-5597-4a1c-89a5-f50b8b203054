import React, { useEffect, useRef, useCallback } from 'react';
import 'luckysheet/dist/plugins/css/pluginsCss.css';
import 'luckysheet/dist/plugins/plugins.css';
import 'luckysheet/dist/css/luckysheet.css';
import 'luckysheet/dist/assets/iconfont/iconfont.css';
import luckysheet from 'luckysheet';

// Helper function to convert column index to letter (0 -> 'A', 25 -> 'Z', 26 -> 'AA', etc.)
const getColumnLetter = (colIndex) => {
  let columnLetter = '';
  let col = colIndex + 1; // Convert to 1-based
  
  while (col > 0) {
    const remainder = (col - 1) % 26;
    columnLetter = String.fromCharCode(65 + remainder) + columnLetter;
    col = Math.floor((col - 1) / 26);
  }
  
  return columnLetter || 'A';
};

// Convert cell coordinates to Excel reference (e.g., {r:0,c:2} -> 'C1')
const getCellRef = (r, c) => {
  return `${getColumnLetter(c)}${r + 1}`;
};

const transformToLuckysheetData = (inputData = {}) => {
  // Initialize a basic 20x10 grid
  const rowCount = 20;
  const colCount = 10;
  const data = Array(rowCount).fill().map(() => Array(colCount).fill(null));
  
  // Process any initial data if provided
  if (inputData && typeof inputData === 'object') {
    for (const key in inputData) {
      if (key === 'currentCell' || key === 'navigatedCell') continue;
      
      const match = key.match(/^([A-Z]+)(\d+)$/i);
      if (!match) continue;
      
      const colStr = match[1].toUpperCase();
      const rowNum = parseInt(match[2], 10);
      
      // Convert column letters to 0-based index
      let colIndex = 0;
      for (let i = 0; i < colStr.length; i++) {
        colIndex = colIndex * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
      }
      colIndex--; // Convert to 0-based index
      
      const rowIndex = rowNum - 1; // Convert to 0-based index
      
      // Only process if within our grid
      if (rowIndex >= 0 && rowIndex < rowCount && colIndex >= 0 && colIndex < colCount) {
        data[rowIndex][colIndex] = { v: inputData[key] };
      }
    }
  }
  
  // Fill in any null values with empty objects
  for (let r = 0; r < rowCount; r++) {
    for (let c = 0; c < colCount; c++) {
      if (data[r][c] === null) {
        data[r][c] = { v: '' };
      }
    }
  }
  
  return [{
    name: 'Sheet1',
    data,
    row: rowCount,
    column: colCount,
    defaultRowHeight: 25,
    defaultColWidth: 100,
    config: {
      merge: {},
      rowlen: {},
      columnlen: {},
      rowhidden: {},
      colhidden: {},
      borderInfo: [],
    },
    luckysheet_select_save: [],
    calcChain: [],
    celldata: []
  }];
};

const ReactSpreadsheetWrapper = ({ onCellChange, onCellSelect, initialData = {}, readOnly = false }) => {
  const luckysheetId = 'luckysheet-container';
  const isMounted = useRef(true);
  const selectionTimeout = useRef(null);

  // Cleanup function for the Luckysheet instance
  const cleanupLuckysheet = useCallback(() => {
    try {
      if (luckysheet.destroy && typeof luckysheet.destroy === 'function') {
        luckysheet.destroy();
      }
    } catch (e) {
      console.warn('Error during Luckysheet cleanup:', e);
    }
  }, []);

  // Handle cell changes
  const handleCellChange = useCallback((cell, value, oldValue) => {
    if (!cell || !onCellChange) return;
    
    const cellRef = getCellRef(cell.r, cell.c);
    
    onCellChange({
      r: cell.r,
      c: cell.c,
      ref: cellRef,
      v: value,
      format: cell.format
    }, value, oldValue);
  }, [onCellChange]);

  // Handle cell selection with debounce
  const handleCellSelect = useCallback((cell) => {
    if (!cell || !onCellSelect) return;
    
    // Debounce selection updates
    if (selectionTimeout.current) {
      clearTimeout(selectionTimeout.current);
    }
    
    selectionTimeout.current = setTimeout(() => {
      if (!isMounted.current) return;
      
      const cellRef = getCellRef(cell.r, cell.c);
      
      onCellSelect({
        r: cell.r,
        c: cell.c,
        ref: cellRef,
        format: cell.format
      });
    }, 50);
  }, [onCellSelect]);

  // Initialize Luckysheet
  useEffect(() => {
    isMounted.current = true;
    
    const initLuckysheet = () => {
      if (!isMounted.current) return;
      
      try {
        // Clean up any existing instance
        cleanupLuckysheet();

        // Prepare data
        const sheetData = transformToLuckysheetData(initialData || {});
        
        // Initialize Luckysheet with options
        const options = {
          container: luckysheetId,
          title: 'Spreadsheet',
          lang: 'en',
          showinfobar: false,
          showsheetbar: false,
          showtoolbar: true,
          showstatisticBar: true,
          data: sheetData,
          allowCopy: !readOnly,
          allowEdit: !readOnly,
          enableAddRow: !readOnly,
          enableAddCol: !readOnly,
          enablePage: false,
          row: {
            len: 20,
            height: 25,
          },
          column: {
            len: 10,
            width: 100,
          },
          hook: {
            cellUpdated: (cell, value, oldValue) => {
              if (isMounted.current) {
                handleCellChange(cell, value, oldValue);
              }
            },
            cellClickBefore: (e, cell) => {
              if (isMounted.current && cell) {
                handleCellSelect(cell);
              }
              return !readOnly;
            },
            cellSelectBefore: (cell) => {
              if (isMounted.current && cell) {
                handleCellSelect(cell);
              }
              return true;
            }
          }
        };

        // Initialize Luckysheet
        if (document.getElementById(luckysheetId)) {
          luckysheet.create(options);
        }
      } catch (error) {
        console.error('Error initializing Luckysheet:', error);
      }
    };

    // Initialize with a small delay to ensure DOM is ready
    const timer = setTimeout(initLuckysheet, 100);

    // Cleanup function
    return () => {
      isMounted.current = false;
      clearTimeout(timer);
      cleanupLuckysheet();
      if (selectionTimeout.current) {
        clearTimeout(selectionTimeout.current);
      }
    };
  }, [initialData, readOnly, cleanupLuckysheet, handleCellChange, handleCellSelect]);

  return (
    <div 
      id={luckysheetId}
      style={{
        width: '100%',
        height: '500px',
        margin: '0 auto',
        position: 'relative'
      }}
    />
  );
};

export default ReactSpreadsheetWrapper;
