import React, { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'

// Global error handler for Luckysheet issues
window.addEventListener('error', (event) => {
  if (event.error && event.error.message) {
    const message = event.error.message;
    // Suppress known Luckysheet errors
    if (message.includes('objectInnerText') ||
        message.includes('NaN') && message.includes('width') ||
        message.includes('sparkline') ||
        message.includes('luckysheet') ||
        message.includes('Cannot read properties of undefined')) {
      console.warn('Original error suppressed:', message);
      event.preventDefault();
      return false;
    }
  }
  // Also check for script errors from sparkline.js
  if (event.filename && event.filename.includes('sparkline.js')) {
    console.warn('Sparkline script error suppressed:', event);
    event.preventDefault();
    return false;
  }
});

// Global unhandled rejection handler
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message) {
    const message = event.reason.message;
    if (message.includes('sparkline') ||
        message.includes('objectInnerText') ||
        message.includes('luckysheet') ||
        message.includes('Cannot read properties of undefined')) {
      console.warn('Original promise rejection suppressed:', message);
      event.preventDefault();
      return false;
    }
  }
});

// Suppress React development warnings about NaN values
const originalConsoleWarn = console.warn;
console.warn = (...args) => {
  const message = args.join(' ');
  if (message.includes('NaN') && message.includes('css style property') ||
      message.includes('sparkline')) {
    // Suppress NaN CSS warnings and sparkline warnings
    return;
  }
  originalConsoleWarn.apply(console, args);
};

// Add a global error handler for script loading errors
document.addEventListener('error', (event) => {
  if (event.target.tagName === 'SCRIPT' && event.target.src.includes('sparkline')) {
    console.warn('Sparkline script loading error suppressed:', event);
    event.preventDefault();
    return false;
  }
}, true);

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
