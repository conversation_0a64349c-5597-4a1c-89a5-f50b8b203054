import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import Navbar from './components/Navbar';
import ProtectedRoute from './components/ProtectedRoute';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import Level from './pages/Level';
import Challenge from './pages/Challenge.univer';
import Leaderboard from './pages/Leaderboard';
import TestValidation from './pages/TestValidation';
import TestChallenge4 from './pages/TestChallenge4';
import TestUniver from './pages/TestUniver';

function App() {
  return (
    <LanguageProvider>
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <Navbar />
            <main className="container mx-auto px-4 py-8">
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route path="/dashboard" element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                } />
                <Route path="/level/:levelId" element={
                  <ProtectedRoute>
                    <Level />
                  </ProtectedRoute>
                } />
                <Route path="/challenge/:challengeId" element={
                  <ProtectedRoute>
                    <Challenge />
                  </ProtectedRoute>
                } />
                <Route path="/leaderboard" element={
                  <ProtectedRoute>
                    <Leaderboard />
                  </ProtectedRoute>
                } />
                <Route path="/test-validation" element={<TestValidation />} />
                <Route path="/test-challenge-4" element={<TestChallenge4 />} />
                <Route path="/test-univer" element={<TestUniver />} />
              </Routes>
            </main>
          </div>
        </Router>
      </AuthProvider>
    </LanguageProvider>
  );
}

export default App;
