@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Excel-like interface */
.excel-cell {
  @apply bg-white text-sm font-mono;
  border: 1px solid #d1d5db !important;
  border-collapse: separate !important;
  min-height: 32px;
  height: 32px;
  width: 80px;
  position: relative;
  padding: 2px 4px;
  vertical-align: middle;
  box-sizing: border-box;
}

.excel-cell:hover {
  background-color: #f8fafc !important;
}

.excel-cell.selected {
  background-color: #dbeafe !important;
  border: 2px solid #3b82f6 !important;
  z-index: 10;
}

.excel-header {
  background-color: #f3f4f6 !important;
  border: 1px solid #9ca3af !important;
  text-align: center;
  font-weight: 600;
  font-size: 12px;
  color: #374151;
  min-height: 32px;
  height: 32px;
  padding: 4px;
  box-sizing: border-box;
}

/* Excel table container */
.excel-table {
  border-collapse: separate !important;
  border-spacing: 0 !important;
  border: 2px solid #9ca3af;
}

.excel-table td,
.excel-table th {
  border: 1px solid #d1d5db !important;
  margin: 0 !important;
  padding: 0 !important;
}

.excel-cell:focus {
  @apply outline-none ring-2 ring-excel-blue border-excel-blue;
}

.excel-cell.selected {
  @apply bg-blue-100 border-excel-blue;
}

.excel-header {
  @apply bg-excel-gray border border-gray-300 text-center font-semibold text-xs;
}

.challenge-card {
  @apply bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow;
}

.level-card {
  @apply bg-gradient-to-br from-excel-green to-green-600 text-white rounded-lg shadow-lg;
}

.progress-bar {
  @apply bg-gray-200 rounded-full overflow-hidden;
}

.progress-fill {
  @apply bg-excel-green h-full transition-all duration-300;
}

/* Fortune Sheet fixes */
/* 
.fortune-sheet-wrapper {
  position: relative;
  overflow: hidden;
  width: 100% !important;
  height: 500px !important;
}

.fortune-sheet-wrapper * {
  box-sizing: border-box;
}

.fortune-sheet-wrapper .luckysheet-cell-main {
  width: 100% !important;
  height: 100% !important;
}

.fortune-sheet-wrapper .luckysheet-grid-container {
  width: 100% !important;
  height: 100% !important;
}

 Fix for NaN width/height issues - more comprehensive 
.fortune-sheet-wrapper [style*="width: NaN"],
.fortune-sheet-wrapper [style*="width:NaN"] {
  width: auto !important;
}

.fortune-sheet-wrapper [style*="height: NaN"],
.fortune-sheet-wrapper [style*="height:NaN"] {
  height: auto !important;
}

 Prevent any NaN values in styles 
.fortune-sheet-wrapper * {
  min-width: 0 !important;
  min-height: 0 !important;
}

 Ensure proper container sizing 
.fortune-sheet-wrapper > div {
  width: 100% !important;
  height: 500px !important;
  max-width: 100% !important;
  max-height: 500px !important;
}

 Fix toolbar layout 
.fortune-sheet-wrapper .luckysheet-toolbar {
  flex-wrap: wrap !important;
  max-height: 120px !important;
  overflow-y: auto !important;
  width: 100% !important;
}

.fortune-sheet-wrapper .luckysheet-toolbar-button-group {
  margin: 2px !important;
}

 Suppress React warnings about NaN in development 
.fortune-sheet-wrapper .luckysheet-grid-container canvas {
  width: 100% !important;
  height: 100% !important;
}

 Additional error suppression 
.fortune-sheet-wrapper .luckysheet-scrollbar {
  width: auto !important;
  height: auto !important;
}
*/

/* Luckysheet specific styles can be added here if needed,
   but primarily its own CSS files should handle styling. */
