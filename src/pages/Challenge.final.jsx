import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { CheckCircle, XCircle, ArrowLeft, Lightbulb } from 'lucide-react';

// Import Univer component with error boundary
import ReactSpreadsheetWrapper from '../components/ReactSpreadsheetWrapper.fallback';

// Simple Error Boundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Spreadsheet component error:', error, errorInfo);
    if (this.props.onError) {
      this.props.onError(error);
    }
  }

  render() {
    if (this.state.hasError) {
      return null; // Let parent handle the error display
    }
    return this.props.children;
  }
}

const Challenge = () => {
  const { challengeId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { t } = useLanguage();
  
  console.log('Challenge component mounted. challengeId:', challengeId);
  
  const [challenge, setChallenge] = useState(null);
  const [allChallenges, setAllChallenges] = useState([]);
  const [userSolution, setUserSolution] = useState({});
  const [feedback, setFeedback] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCell, setSelectedCell] = useState('A1');
  const [spreadsheetError, setSpreadsheetError] = useState(false);

  // Mock challenge data
  const mockChallenges = [
    {
      id: '1-1',
      title: 'Select Cell C3',
      description: 'Click on cell C3 to select it.',
      instructions: ['Click on cell C3 in the spreadsheet'],
      expectedSolution: { targetCell: 'C3' },
      initialData: {},
      hint: 'Use your mouse to click on the cell at column C, row 3'
    },
    {
      id: '1-2',
      title: 'Enter Text in A1',
      description: 'Type "Hello Excel" in cell A1.',
      instructions: ['Click on cell A1', 'Type "Hello Excel"', 'Press Enter'],
      expectedSolution: { cellA1: 'Hello Excel' },
      initialData: {},
      hint: 'Double-click the cell to start editing, then type your text'
    },
    {
      id: '1-3',
      title: 'Basic Cell Formatting',
      description: 'Learn to format cells using bold, italic, and colors.',
      instructions: ['Click on cell A1', 'Type "Formatted Text"', 'Apply bold formatting', 'Change background color to yellow'],
      expectedSolution: { cellA1: 'Formatted Text', formatting: 'bold' },
      initialData: {},
      hint: 'Use the formatting toolbar to apply styles to your text'
    },
    {
      id: '1-4',
      title: 'Simple Calculation',
      description: 'Perform basic arithmetic operations.',
      instructions: ['Enter 10 in cell A1', 'Enter 20 in cell B1', 'Create a formula in C1 to add A1 and B1'],
      expectedSolution: { cellA1: '10', cellB1: '20', cellC1: '=A1+B1' },
      initialData: {},
      hint: 'Start your formula with = sign, then type A1+B1'
    }
  ];

  // Fetch challenge data
  useEffect(() => {
    console.log('useEffect triggered. challengeId:', challengeId);
    
    const fetchChallenge = () => {
      try {
        console.log('Starting to fetch challenge...');
        setLoading(true);
        
        const currentChallenge = mockChallenges.find(c => c.id === challengeId);
        if (currentChallenge) {
          console.log('Found challenge:', currentChallenge);
          setChallenge(currentChallenge);
          setAllChallenges(mockChallenges);
          
          // Set initial data if provided
          if (currentChallenge.initialData) {
            setUserSolution(currentChallenge.initialData);
          }
        } else {
          console.log('Challenge not found for ID:', challengeId);
          setError('Challenge not found');
        }
      } catch (err) {
        console.error('Error fetching challenge:', err);
        setError('Failed to load challenge');
      } finally {
        setLoading(false);
      }
    };

    if (challengeId) {
      fetchChallenge();
    }
  }, [challengeId]);

  // Handle back navigation
  const handleBack = () => {
    const levelId = challengeId.split('-')[0];
    navigate(`/level/${levelId}`);
  };

  // Handle cell changes from Univer
  const handleCellChange = (data) => {
    console.log('Cell data changed:', data);
    setUserSolution(data);
  };

  // Handle cell selection from Univer
  const handleCellSelect = (cellRef) => {
    console.log('Cell selected:', cellRef);
    setSelectedCell(cellRef);
  };

  // Get Univer cell value
  const getUniverCellValue = (cellRef) => {
    try {
      return userSolution[cellRef] || '';
    } catch (error) {
      console.error('Error getting cell value:', error);
      return '';
    }
  };

  // Validate solution locally
  const validateSolutionLocally = (submissionData) => {
    try {
      const expected = challenge?.expectedSolution;
      if (!expected) {
        console.error('No expected solution data found');
        return false;
      }

      // Challenge 1-1: Check if C3 is selected
      if (challengeId === '1-1') {
        const isCorrect = submissionData.selectedCell === 'C3';
        console.log('Selected cell:', submissionData.selectedCell, 'Expected: C3', 'Is correct?', isCorrect);
        return isCorrect;
      }

      // Challenge 1-2: Check if A1 contains "Hello Excel"
      if (challengeId === '1-2') {
        const cellValue = submissionData.cellA1;
        const isCorrect = cellValue === 'Hello Excel';
        console.log('Cell A1 value:', cellValue, 'Expected: Hello Excel', 'Is correct?', isCorrect);
        return isCorrect;
      }

      // Challenge 1-3: Check if A1 contains "Formatted Text"
      if (challengeId === '1-3') {
        const cellValue = submissionData.cellA1;
        const isCorrect = cellValue === 'Formatted Text';
        console.log('Cell A1 value:', cellValue, 'Expected: Formatted Text', 'Is correct?', isCorrect);
        return isCorrect;
      }

      // Challenge 1-4: Check calculation challenge
      if (challengeId === '1-4') {
        const cellA1 = submissionData.cellA1;
        const cellB1 = submissionData.cellB1;
        const cellC1 = submissionData.cellC1;
        const isCorrect = cellA1 === '10' && cellB1 === '20' && (cellC1 === '=A1+B1' || cellC1 === '30');
        console.log('Cells:', { A1: cellA1, B1: cellB1, C1: cellC1 }, 'Is correct?', isCorrect);
        return isCorrect;
      }

      return false;
    } catch (error) {
      console.error('Error in local validation:', error);
      return false;
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    setSubmitting(true);
    setFeedback(null);

    try {
      // Get cell data from userSolution
      const submissionData = {
        cellA1: userSolution.A1 || '',
        cellB1: userSolution.B1 || '',
        cellC1: userSolution.C1 || '',
        selectedCell: selectedCell,
        timestamp: new Date().toISOString()
      };

      console.log('Submitting solution data:', submissionData);
      console.log('Current userSolution:', userSolution);

      // Use local validation
      const isCorrect = validateSolutionLocally(submissionData);
      const result = {
        correct: isCorrect,
        message: isCorrect ? 'Correct! Well done!' : 'Not quite right. Try again!',
        score: isCorrect ? 100 : 0
      };

      setFeedback(result);
    } catch (error) {
      console.error('Error validating solution:', error);
      setFeedback({
        correct: false,
        message: 'Error submitting solution. Please try again.',
        score: 0
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle reset
  const handleReset = () => {
    setUserSolution(challenge?.initialData || {});
    setFeedback(null);
    setSelectedCell('A1');
  };

  // Get next challenge
  const getNextChallenge = () => {
    const currentIndex = allChallenges.findIndex(c => c.id === challengeId);
    return currentIndex < allChallenges.length - 1 ? allChallenges[currentIndex + 1] : null;
  };

  // Handle continue to next challenge
  const handleContinue = () => {
    const nextChallenge = getNextChallenge();
    if (nextChallenge) {
      navigate(`/challenge/${nextChallenge.id}`);
    } else {
      navigate('/dashboard');
    }
  };

  if (loading) {
    console.log('Showing loading state');
    return <div className="p-4">Loading...</div>;
  }
  
  if (error) {
    console.log('Showing error state:', error);
    return <div className="p-4 text-red-600">{error}</div>;
  }
  
  if (!challenge) {
    console.log('No challenge found');
    return <div className="p-4">Challenge not found</div>;
  }

  console.log('Rendering challenge:', challenge);

  const nextChallenge = getNextChallenge();

  return (
    <div className="container mx-auto p-4 max-w-5xl">
      <div className="mb-6">
        <button 
          onClick={handleBack}
          className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
        >
          <ArrowLeft className="w-5 h-5 mr-1" />
          Back
        </button>
        
        <h1 className="text-2xl font-bold mb-2">{challenge.title}</h1>
        <p className="text-gray-600 mb-4">{challenge.description}</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Instructions Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <Lightbulb className="w-5 h-5 mr-2 text-yellow-500" />
              Instructions
            </h2>
            <div className="space-y-3">
              {Array.isArray(challenge.instructions) ? challenge.instructions.map((instruction, index) => (
                <div key={index} className="flex items-start">
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full mr-3 mt-0.5">
                    {index + 1}
                  </span>
                  <p className="text-sm text-gray-700">{instruction}</p>
                </div>
              )) : (
                <div className="flex items-start">
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full mr-3 mt-0.5">
                    1
                  </span>
                  <p className="text-sm text-gray-700">
                    {challenge.instructions || 'No instructions available'}
                  </p>
                </div>
              )}
            </div>
            
            {challenge.hint && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-sm text-yellow-800">
                  <strong>Hint:</strong> {challenge.hint}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Spreadsheet Panel */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4">Your Solution</h2>
            
            <div className="w-full h-96">
              <ReactSpreadsheetWrapper
                initialData={userSolution}
                onCellChange={handleCellChange}
                onCellSelect={handleCellSelect}
                readOnly={false}
              />
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex justify-between items-center mt-4">
        <div className="flex-1">
          <button
            onClick={handleReset}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 mr-4"
            disabled={submitting}
          >
            Reset
          </button>
        </div>
        
        <div className="flex items-center space-x-4">
          {feedback && (
            <div 
              className={`flex items-center px-3 py-2 rounded-md ${
                feedback.correct 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}
            >
              {feedback.correct ? (
                <CheckCircle className="w-4 h-4 mr-2" />
              ) : (
                <XCircle className="w-4 h-4 mr-2" />
              )}
              <span>{feedback.message}</span>
            </div>
          )}
          
          <button
            onClick={handleSubmit}
            disabled={submitting}
            className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 min-w-[100px] text-center"
          >
            {submitting ? 'Submitting...' : 'Submit'}
          </button>
        </div>
      </div>
      
      {feedback?.correct && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-md">
          <div className="flex items-center">
            <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
            <p className="font-medium">Challenge Complete!</p>
          </div>
          <div className="mt-4 flex justify-end">
            <button
              onClick={handleContinue}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              {nextChallenge ? 'Next Challenge' : 'Back to Dashboard'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Challenge;
