import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { BookOpen, Target, Trophy, Users, ArrowRight } from 'lucide-react';

const Home = () => {
  const { isAuthenticated } = useAuth();
  const { t } = useLanguage();

  const features = [
    {
      icon: <BookOpen className="h-8 w-8 text-excel-green" />,
      title: t('interactiveLearningTitle'),
      description: t('interactiveLearningDesc')
    },
    {
      icon: <Target className="h-8 w-8 text-excel-green" />,
      title: t('gamifiedChallengesTitle'),
      description: t('gamifiedChallengesDesc')
    },
    {
      icon: <Trophy className="h-8 w-8 text-excel-green" />,
      title: t('trackProgressTitle'),
      description: t('trackProgressDesc')
    },
    {
      icon: <Users className="h-8 w-8 text-excel-green" />,
      title: t('communityLearningTitle'),
      description: t('communityLearningDesc')
    }
  ];

  return (
    <div className="max-w-6xl mx-auto">
      {/* Hero Section */}
      <div className="text-center py-16">
        <h1 className="text-5xl font-bold text-gray-900 mb-6">
          {t('heroTitle')}
          <span className="text-excel-green"> {t('heroTitleHighlight')}</span>
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          {t('heroDescription')}
        </p>

        <div className="flex justify-center space-x-4">
          {isAuthenticated ? (
            <Link
              to="/dashboard"
              className="bg-excel-green text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors flex items-center space-x-2"
            >
              <span>{t('continueLearning')}</span>
              <ArrowRight className="h-5 w-5" />
            </Link>
          ) : (
            <>
              <Link
                to="/register"
                className="bg-excel-green text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <span>{t('startLearning')}</span>
                <ArrowRight className="h-5 w-5" />
              </Link>
              <Link
                to="/login"
                className="border-2 border-excel-green text-excel-green px-8 py-3 rounded-lg font-semibold hover:bg-excel-green hover:text-white transition-colors"
              >
                {t('signIn')}
              </Link>
            </>
          )}
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16">
        <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
          {t('whyChoose')}
        </h2>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="flex justify-center mb-4">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Learning Path Preview */}
      <div className="py-16 bg-gray-50 rounded-lg">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {t('learningJourney')}
          </h2>
          <p className="text-lg text-gray-600">
            {t('learningJourneyDesc')}
          </p>
        </div>

        <div className="grid md:grid-cols-5 gap-4">
          {[
            { level: 1, title: t('level1Title'), description: t('interfaceNavigation') },
            { level: 2, title: t('level2Title'), description: t('sumAverageIf') },
            { level: 3, title: t('level3Title'), description: t('sortingFiltering') },
            { level: 4, title: t('level4Title'), description: t('vlookupIndexMatch') },
            { level: 5, title: t('level5Title'), description: t('pivotTablesCharts') }
          ].map((level, index) => (
            <div key={index} className="text-center p-4 bg-white rounded-lg shadow-sm">
              <div className="w-12 h-12 bg-excel-green text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold">
                {level.level}
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">{level.title}</h4>
              <p className="text-sm text-gray-600">{level.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Home;
