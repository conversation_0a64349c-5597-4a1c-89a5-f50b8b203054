import React, { useState } from 'react';
import ReactSpreadsheetWrapper from '../components/ReactSpreadsheetWrapper.fallback';

const TestUniver = () => {
  const [cellData, setCellData] = useState({
    A1: 'Hello',
    B1: 'World',
    C1: '=A1&" "&B1'
  });
  const [selectedCell, setSelectedCell] = useState('A1');

  const handleCellChange = (newData) => {
    console.log('Cell data changed:', newData);
    setCellData(newData);
  };

  const handleCellSelect = (cellRef) => {
    console.log('Cell selected:', cellRef);
    setSelectedCell(cellRef);
  };

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Univer Spreadsheet Test</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Current State</h2>
        <div className="bg-gray-100 p-4 rounded-lg">
          <p><strong>Selected Cell:</strong> {selectedCell}</p>
          <p><strong>Cell Data:</strong></p>
          <pre className="mt-2 text-sm">{JSON.stringify(cellData, null, 2)}</pre>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Univer Spreadsheet</h2>
        <div style={{ width: '100%', height: '500px' }}>
          <ReactSpreadsheetWrapper
            initialData={cellData}
            onCellChange={handleCellChange}
            onCellSelect={handleCellSelect}
            readOnly={false}
          />
        </div>
      </div>

      <div className="mt-6">
        <h2 className="text-xl font-semibold mb-4">Test Instructions</h2>
        <ul className="list-disc list-inside space-y-2 text-gray-700">
          <li>The spreadsheet should load with initial data in cells A1, B1, and C1</li>
          <li>Try clicking on different cells to test selection</li>
          <li>Try editing cell values to test the change callback</li>
          <li>Check if formulas work (C1 should concatenate A1 and B1)</li>
          <li>Verify that the state updates are reflected in the "Current State" section above</li>
        </ul>
      </div>
    </div>
  );
};

export default TestUniver;
