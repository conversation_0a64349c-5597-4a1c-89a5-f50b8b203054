import React, { useState } from 'react';

const TestValidation = () => {
  const [testData, setTestData] = useState({
    A1: 10,
    B1: 20,
    C1: '=A1+B1'
  });
  const [result, setResult] = useState(null);

  const testValidation = async () => {
    try {
      // Test local validation logic
      const challenge = {
        id: '1-4',
        solutionData: {
          cellA1: 10,
          cellB1: 20,
          cellC1: '=A1+B1'
        }
      };

      const validateSolution = (challenge, userSolution) => {
        const normalizeValue = (value) => {
          if (value === null || value === undefined) return '';
          return String(value).trim();
        };

        const expected = challenge.solutionData;

        console.log('=== Local Validation Test ===');
        console.log('User solution:', JSON.stringify(userSolution, null, 2));
        console.log('Expected solution:', JSON.stringify(expected, null, 2));

        // Check A1
        const userA1 = normalizeValue(userSolution.A1);
        const expectedA1 = expected.cellA1;
        const a1Valid = Number(userA1) === Number(expectedA1);
        console.log(`A1: "${userA1}" vs "${expectedA1}" = ${a1Valid}`);

        // Check B1
        const userB1 = normalizeValue(userSolution.B1);
        const expectedB1 = expected.cellB1;
        const b1Valid = Number(userB1) === Number(expectedB1);
        console.log(`B1: "${userB1}" vs "${expectedB1}" = ${b1Valid}`);

        // Check C1
        const userC1 = normalizeValue(userSolution.C1);
        const expectedC1 = expected.cellC1;
        const c1Valid = userC1 === expectedC1 ||
                        userC1 === '=A1+B1' ||
                        userC1.toLowerCase() === '=a1+b1' ||
                        userC1 === '= A1+B1' ||
                        userC1 === '= A1 + B1' ||
                        userC1 === '=A1 + B1' ||
                        Number(userC1) === 30;
        console.log(`C1: "${userC1}" vs "${expectedC1}" = ${c1Valid}`);

        const result = a1Valid && b1Valid && c1Valid;
        console.log(`Final result: ${result}`);

        return result;
      };

      const isCorrect = validateSolution(challenge, testData);
      setResult({
        correct: isCorrect,
        message: isCorrect ? 'Correct! Well done!' : 'Not quite right. Try again!',
        score: isCorrect ? 25 : 0,
        localValidation: true
      });
    } catch (error) {
      setResult({ error: error.message });
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Validation Test</h1>

      <div className="mb-4">
        <h2 className="text-lg font-semibold mb-2">Test Data:</h2>
        <pre className="bg-gray-100 p-4 rounded">
          {JSON.stringify(testData, null, 2)}
        </pre>
      </div>

      <button
        onClick={testValidation}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      >
        Test Validation
      </button>

      {result && (
        <div className="mt-4">
          <h2 className="text-lg font-semibold mb-2">Result:</h2>
          <pre className="bg-gray-100 p-4 rounded">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-8">
        <h2 className="text-lg font-semibold mb-2">Edit Test Data:</h2>
        <div className="space-y-2">
          <div>
            <label className="block text-sm font-medium">A1:</label>
            <input
              type="text"
              value={testData.A1}
              onChange={(e) => setTestData({...testData, A1: e.target.value})}
              className="border border-gray-300 rounded px-2 py-1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium">B1:</label>
            <input
              type="text"
              value={testData.B1}
              onChange={(e) => setTestData({...testData, B1: e.target.value})}
              className="border border-gray-300 rounded px-2 py-1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium">C1:</label>
            <input
              type="text"
              value={testData.C1}
              onChange={(e) => setTestData({...testData, C1: e.target.value})}
              className="border border-gray-300 rounded px-2 py-1"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestValidation;
