import React from 'react';
import { useParams } from 'react-router-dom';

const ChallengeTest = () => {
  const { challengeId } = useParams();
  
  console.log('ChallengeTest component mounted. challengeId:', challengeId);
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Challenge Test Page</h1>
      <p>Challenge ID: {challengeId}</p>
      <p>This is a test page to verify routing works.</p>
    </div>
  );
};

export default ChallengeTest;
