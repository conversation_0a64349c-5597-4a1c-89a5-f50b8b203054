import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { CheckCircle, XCircle, ArrowLeft, Lightbulb } from 'lucide-react';

// Import Univer component with error boundary
let ReactSpreadsheetWrapper = null;
try {
  ReactSpreadsheetWrapper = require('../components/ReactSpreadsheetWrapper.fixed').default;
} catch (error) {
  console.warn('Failed to load ReactSpreadsheetWrapper:', error);
}

const Challenge = () => {
  const { challengeId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { t } = useLanguage();
  
  console.log('Challenge component mounted. challengeId:', challengeId);
  
  const [challenge, setChallenge] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [feedback, setFeedback] = useState(null);
  const [submitting, setSubmitting] = useState(false);

  // Mock challenge data
  const mockChallenges = [
    {
      id: '1-1',
      title: 'Select Cell C3',
      description: 'Click on cell C3 to select it.',
      instructions: ['Click on cell C3 in the spreadsheet'],
      expectedSolution: { targetCell: 'C3' },
      initialData: {}
    },
    {
      id: '1-2',
      title: 'Enter Text in A1',
      description: 'Type "Hello Excel" in cell A1.',
      instructions: ['Click on cell A1', 'Type "Hello Excel"', 'Press Enter'],
      expectedSolution: { cellA1: 'Hello Excel' },
      initialData: {}
    }
  ];

  // Fetch challenge data
  useEffect(() => {
    console.log('useEffect triggered. challengeId:', challengeId);
    
    const fetchChallenge = () => {
      try {
        console.log('Starting to fetch challenge...');
        setLoading(true);
        
        const currentChallenge = mockChallenges.find(c => c.id === challengeId);
        if (currentChallenge) {
          console.log('Found challenge:', currentChallenge);
          setChallenge(currentChallenge);
        } else {
          console.log('Challenge not found for ID:', challengeId);
          setError('Challenge not found');
        }
      } catch (err) {
        console.error('Error fetching challenge:', err);
        setError('Failed to load challenge');
      } finally {
        setLoading(false);
      }
    };

    if (challengeId) {
      fetchChallenge();
    }
  }, [challengeId]);

  // Handle back navigation
  const handleBack = () => {
    const levelId = challengeId.split('-')[0];
    navigate(`/level/${levelId}`);
  };

  // Handle form submission
  const handleSubmit = async () => {
    setSubmitting(true);
    setFeedback(null);
    
    try {
      console.log('Submitting solution...');
      
      // Mock validation
      const isCorrect = Math.random() > 0.5; // Random success for testing
      
      const result = {
        correct: isCorrect,
        message: isCorrect ? 'Correct! Well done!' : 'Not quite right. Try again!',
        score: isCorrect ? 100 : 0
      };

      setFeedback(result);
    } catch (error) {
      console.error('Error validating solution:', error);
      setFeedback({
        correct: false,
        message: 'Error submitting solution. Please try again.',
        score: 0
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle reset
  const handleReset = () => {
    setFeedback(null);
  };

  if (loading) {
    console.log('Showing loading state');
    return <div className="p-4">Loading...</div>;
  }
  
  if (error) {
    console.log('Showing error state:', error);
    return <div className="p-4 text-red-600">{error}</div>;
  }
  
  if (!challenge) {
    console.log('No challenge found');
    return <div className="p-4">Challenge not found</div>;
  }

  console.log('Rendering challenge:', challenge);

  return (
    <div className="container mx-auto p-4 max-w-5xl">
      <div className="mb-6">
        <button 
          onClick={handleBack}
          className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
        >
          <ArrowLeft className="w-5 h-5 mr-1" />
          Back
        </button>
        
        <h1 className="text-2xl font-bold mb-2">{challenge.title}</h1>
        <p className="text-gray-600 mb-4">{challenge.description}</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Instructions Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <Lightbulb className="w-5 h-5 mr-2 text-yellow-500" />
              Instructions
            </h2>
            <div className="space-y-3">
              {Array.isArray(challenge.instructions) ? challenge.instructions.map((instruction, index) => (
                <div key={index} className="flex items-start">
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full mr-3 mt-0.5">
                    {index + 1}
                  </span>
                  <p className="text-sm text-gray-700">{instruction}</p>
                </div>
              )) : (
                <div className="flex items-start">
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full mr-3 mt-0.5">
                    1
                  </span>
                  <p className="text-sm text-gray-700">
                    {challenge.instructions || 'No instructions available'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Spreadsheet Panel */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4">Your Solution</h2>

            <div className="w-full h-96">
              {ReactSpreadsheetWrapper ? (
                <ReactSpreadsheetWrapper
                  initialData={{}}
                  onCellChange={(data) => console.log('Cell changed:', data)}
                  onCellSelect={(cellRef) => console.log('Cell selected:', cellRef)}
                  readOnly={false}
                />
              ) : (
                <div className="w-full h-full bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center">
                  <p className="text-gray-500">Spreadsheet component failed to load</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex justify-between items-center mt-4">
        <div className="flex-1">
          <button
            onClick={handleReset}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 mr-4"
            disabled={submitting}
          >
            Reset
          </button>
        </div>
        
        <div className="flex items-center space-x-4">
          {feedback && (
            <div 
              className={`flex items-center px-3 py-2 rounded-md ${
                feedback.correct 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}
            >
              {feedback.correct ? (
                <CheckCircle className="w-4 h-4 mr-2" />
              ) : (
                <XCircle className="w-4 h-4 mr-2" />
              )}
              <span>{feedback.message}</span>
            </div>
          )}
          
          <button
            onClick={handleSubmit}
            disabled={submitting}
            className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 min-w-[100px] text-center"
          >
            {submitting ? 'Submitting...' : 'Submit'}
          </button>
        </div>
      </div>
      
      {feedback?.correct && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-md">
          <div className="flex items-center">
            <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
            <p className="font-medium">Challenge Complete!</p>
          </div>
          <div className="mt-4 flex justify-end">
            <button
              onClick={() => navigate('/dashboard')}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Challenge;
