import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { challengesAPI } from '../services/api';
import ReactSpreadsheetWrapper from '../components/ReactSpreadsheetWrapper.fixed';
import { CheckCircle, XCircle, ArrowLeft, Lightbulb } from 'lucide-react';

const Challenge = () => {
  const { challengeId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { t } = useLanguage();
  
  const [challenge, setChallenge] = useState(null);
  const [allChallenges, setAllChallenges] = useState([]);
  const [userSolution, setUserSolution] = useState({});
  const [feedback, setFeedback] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCell, setSelectedCell] = useState('A1');
  
  // Ref to access Univer instance
  const univerRef = useRef(null);

  // Fetch challenge data
  useEffect(() => {
    const fetchChallenges = async () => {
      try {
        setLoading(true);

        // Try to get challenges from API, fall back to mock data
        let challengesData;
        try {
          challengesData = await challengesAPI.getChallenges();
          console.log('API response:', challengesData);

          // Handle different response formats
          if (challengesData && challengesData.data && Array.isArray(challengesData.data)) {
            challengesData = challengesData.data;
          } else if (!Array.isArray(challengesData)) {
            throw new Error('Invalid response format');
          }
        } catch (apiError) {
          console.warn('API failed, using mock data:', apiError);
          // Fallback to mock data
          challengesData = [
            {
              id: '1-1',
              title: 'Select Cell C3',
              description: 'Click on cell C3 to select it.',
              instructions: ['Click on cell C3 in the spreadsheet'],
              expectedSolution: { targetCell: 'C3' },
              initialData: {}
            },
            {
              id: '1-2',
              title: 'Enter Text in A1',
              description: 'Type "Hello Excel" in cell A1.',
              instructions: ['Click on cell A1', 'Type "Hello Excel"', 'Press Enter'],
              expectedSolution: { cellA1: 'Hello Excel' },
              initialData: {}
            }
          ];
        }

        setAllChallenges(challengesData);

        const currentChallenge = challengesData.find(c => c.id === challengeId);
        if (currentChallenge) {
          setChallenge(currentChallenge);

          // Set initial data if provided
          if (currentChallenge.initialData) {
            setUserSolution(currentChallenge.initialData);
          }
        } else {
          setError('Challenge not found');
        }
      } catch (err) {
        console.error('Error fetching challenges:', err);
        setError('Failed to load challenge');
      } finally {
        setLoading(false);
      }
    };

    if (challengeId) {
      fetchChallenges();
    }
  }, [challengeId]);

  // Handle back navigation
  const handleBack = () => {
    const levelId = challengeId.split('-')[0];
    navigate(`/level/${levelId}`);
  };

  // Handle cell changes from Univer
  const handleCellChange = (data) => {
    console.log('Cell data changed:', data);
    setUserSolution(data);
  };

  // Handle cell selection from Univer
  const handleCellSelect = (cellRef) => {
    console.log('Cell selected:', cellRef);
    setSelectedCell(cellRef);
  };

  // Get Univer cell value
  const getUniverCellValue = (cellRef) => {
    try {
      // For now, get from userSolution state
      // In a full implementation, you'd get this from the Univer instance
      return userSolution[cellRef] || '';
    } catch (error) {
      console.error('Error getting cell value:', error);
      return '';
    }
  };

  // Validate solution locally
  const validateSolutionLocally = (submissionData) => {
    try {
      const expected = challenge?.expectedSolution;
      if (!expected) {
        console.error('No expected solution data found');
        return false;
      }

      // Challenge 1-1: Check if C3 is selected
      if (challengeId === '1-1') {
        const isCorrect = submissionData.selectedCell === 'C3';
        console.log('Selected cell:', submissionData.selectedCell, 'Expected: C3', 'Is correct?', isCorrect);
        return isCorrect;
      }
      
      // Challenge 1-2: Check if A1 contains "Hello Excel"
      if (challengeId === '1-2') {
        const cellValue = submissionData.cellA1;
        const isCorrect = cellValue === 'Hello Excel';
        console.log('Cell A1 value:', cellValue, 'Expected: Hello Excel', 'Is correct?', isCorrect);
        return isCorrect;
      }
      
      // For other challenges, try to validate based on expected data
      if (expected.cellA1 !== undefined) {
        const isCorrect = submissionData.cellA1 === expected.cellA1;
        console.log('Cell A1 value:', submissionData.cellA1, 'Expected:', expected.cellA1, 'Is correct?', isCorrect);
        return isCorrect;
      }
      
      if (expected.targetCell) {
        // For selection-based challenges
        const isCorrect = submissionData.selectedCell === expected.targetCell;
        console.log('Selected cell:', submissionData.selectedCell, 'Expected:', expected.targetCell, 'Is correct?', isCorrect);
        return isCorrect;
      }
      
      return false;
    } catch (error) {
      console.error('Error in local validation:', error);
      return false;
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    setSubmitting(true);
    setFeedback(null);
    
    try {
      // Get cell data for A1 (for challenge 1-2)
      const cellA1Value = getUniverCellValue('A1');
      
      const submissionData = {
        cellA1: cellA1Value,
        selectedCell: selectedCell,
        timestamp: new Date().toISOString()
      };

      console.log('Submitting solution data:', submissionData);

      // Try backend validation first, fall back to local validation
      let result;
      try {
        const response = await challengesAPI.validateSolution(challengeId, submissionData);
        result = response;
      } catch (apiError) {
        console.warn('Backend validation failed, using local validation:', apiError);
        const isCorrect = validateSolutionLocally(submissionData);
        result = {
          correct: isCorrect,
          message: isCorrect ? t('correctSolution') : t('notCorrect'),
          score: isCorrect ? 100 : 0
        };
      }

      setFeedback(result);
    } catch (error) {
      console.error('Error validating solution:', error);
      setFeedback({
        correct: false,
        message: t('submitError'),
        score: 0
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle reset
  const handleReset = () => {
    setUserSolution(challenge?.initialData || {});
    setFeedback(null);
    setSelectedCell('A1');
  };

  // Get next challenge
  const getNextChallenge = () => {
    const currentIndex = allChallenges.findIndex(c => c.id === challengeId);
    return currentIndex < allChallenges.length - 1 ? allChallenges[currentIndex + 1] : null;
  };

  // Handle continue to next challenge
  const handleContinue = () => {
    const nextChallenge = getNextChallenge();
    if (nextChallenge) {
      navigate(`/challenge/${nextChallenge.id}`);
    } else {
      navigate('/dashboard');
    }
  };

  if (loading) return <div className="p-4">{t('loading')}</div>;
  if (error) return <div className="p-4 text-red-600">{error}</div>;
  if (!challenge) return <div className="p-4">Challenge not found</div>;

  const nextChallenge = getNextChallenge();

  return (
    <div className="container mx-auto p-4 max-w-5xl">
      <div className="mb-6">
        <button 
          onClick={handleBack}
          className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
        >
          <ArrowLeft className="w-5 h-5 mr-1" />
          {t('back')}
        </button>
        
        <h1 className="text-2xl font-bold mb-2">
          {t(`challenge_${challenge.id}_title`) !== `challenge_${challenge.id}_title`
            ? t(`challenge_${challenge.id}_title`)
            : challenge.title}
        </h1>
        
        <p className="text-gray-600 mb-4">
          {t(`challenge_${challenge.id}_description`) !== `challenge_${challenge.id}_description`
            ? t(`challenge_${challenge.id}_description`)
            : challenge.description}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Instructions Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <Lightbulb className="w-5 h-5 mr-2 text-yellow-500" />
              {t('instructions')}
            </h2>
            <div className="space-y-3">
              {challenge.instructions?.map((instruction, index) => (
                <div key={index} className="flex items-start">
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full mr-3 mt-0.5">
                    {index + 1}
                  </span>
                  <p className="text-sm text-gray-700">{instruction}</p>
                </div>
              ))}
            </div>
            
            {challenge.hint && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-sm text-yellow-800">
                  <strong>{t('hint')}:</strong> {challenge.hint}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Spreadsheet Panel */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4">{t('yourSolution')}</h2>
            
            <div style={{ width: '100%', height: '400px' }}>
              <ReactSpreadsheetWrapper
                initialData={userSolution}
                onCellChange={handleCellChange}
                onCellSelect={handleCellSelect}
                readOnly={false}
              />
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex justify-between items-center mt-4 relative z-20" style={{ position: 'relative', zIndex: 20 }}>
        <div className="flex-1">
          <button
            onClick={handleReset}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 mr-4"
            disabled={submitting}
            style={{ position: 'relative', zIndex: 30 }}
          >
            {t('reset')}
          </button>
        </div>
        
        <div className="flex items-center space-x-4" style={{ position: 'relative', zIndex: 30 }}>
          {feedback && (
            <div 
              className={`flex items-center px-3 py-2 rounded-md ${
                feedback.correct 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}
            >
              {feedback.correct ? (
                <CheckCircle className="w-4 h-4 mr-2" />
              ) : (
                <XCircle className="w-4 h-4 mr-2" />
              )}
              <span>{feedback.message}</span>
            </div>
          )}
          
          <button
            onClick={handleSubmit}
            disabled={submitting}
            className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 min-w-[100px] text-center"
            style={{ position: 'relative', zIndex: 30 }}
          >
            {submitting ? t('submitting') : t('submit')}
          </button>
        </div>
      </div>
      
      {feedback?.correct && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-md">
          <div className="flex items-center">
            <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
            <p className="font-medium">{t('challengeComplete')}</p>
          </div>
          <div className="mt-4 flex justify-end">
            <button
              onClick={handleContinue}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              {nextChallenge ? t('nextChallenge') : t('backToDashboard')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Challenge;
