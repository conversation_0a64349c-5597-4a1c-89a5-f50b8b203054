import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { challengesAPI, progressAPI } from '../services/api';
import { ArrowLeft, Play, CheckCircle, Lock, Star } from 'lucide-react';

const Level = () => {
  const { levelId } = useParams();
  const { isAuthenticated } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();

  const [level, setLevel] = useState(null);
  const [challenges, setChallenges] = useState([]);
  const [userProgress, setUserProgress] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    const fetchLevelData = async () => {
      try {
        const [levelsResponse, challengesResponse, progressResponse] = await Promise.all([
          challengesAPI.getLevels(),
          challengesAPI.getChallengesByLevel(levelId),
          challengesAPI.getLevelProgress(levelId)
        ]);

        const currentLevel = levelsResponse.data.find(l => l.id === levelId);
        setLevel(currentLevel);
        setChallenges(challengesResponse.data);
        setUserProgress(progressResponse.data);
      } catch (error) {
        console.error('Error fetching level data:', error);
        navigate('/dashboard');
      } finally {
        setLoading(false);
      }
    };

    fetchLevelData();
  }, [levelId, isAuthenticated, navigate]);

  const getChallengeProgress = (challengeId) => {
    return userProgress.find(p => p.challengeId === challengeId);
  };

  const isNextChallengeUnlocked = (currentIndex) => {
    if (currentIndex === 0) return true;
    const previousChallenge = challenges[currentIndex - 1];
    const previousProgress = getChallengeProgress(previousChallenge.id);
    return previousProgress && previousProgress.completed;
  };

  const getCompletedChallenges = () => {
    return userProgress.filter(p => p.completed).length;
  };

  const getTotalPoints = () => {
    return userProgress.reduce((sum, p) => sum + (p.score || 0), 0);
  };

  // Helper function to get translated content
  const getTranslatedContent = (item, field) => {
    // Determine if this is a challenge or level based on the structure
    const isChallenge = item.levelId !== undefined;
    const key = isChallenge
      ? `challenge_${item.id}_${field}`
      : `level_${item.id}_${field}`;
    const translated = t(key);
    // If translation exists and is different from the key, use it; otherwise use original
    return translated !== key ? translated : item[field];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-excel-green"></div>
      </div>
    );
  }

  if (!level) {
    return (
      <div className="text-center py-16">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">{t('levelNotFound')}</h2>
        <Link
          to="/dashboard"
          className="bg-excel-green text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
        >
          {t('backToDashboard')}
        </Link>
      </div>
    );
  }

  const completedChallenges = getCompletedChallenges();
  const totalChallenges = challenges.length;
  const progressPercentage = totalChallenges > 0 ? (completedChallenges / totalChallenges) * 100 : 0;

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <Link
          to="/dashboard"
          className="flex items-center space-x-2 text-excel-green hover:text-green-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{t('backToDashboard')}</span>
        </Link>

        <div className="level-card p-8 mb-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">{t('levelTitle', [level.orderIndex])}: {getTranslatedContent(level, 'title')}</h1>
              <p className="text-green-100 text-lg mb-4">{getTranslatedContent(level, 'description')}</p>
              <div className="flex items-center space-x-4 text-white">
                <span className="bg-green-800 px-3 py-1 rounded-full text-sm">
                  {t(level.difficulty.toLowerCase())}
                </span>
                <span className="flex items-center space-x-1">
                  <Star className="h-4 w-4" />
                  <span>{t('pointsEarnedTotal', [getTotalPoints()])}</span>
                </span>
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-4">
            <div className="flex justify-between text-white text-sm mb-2">
              <span>{t('progress')}</span>
              <span>{t('levelProgress', [completedChallenges, totalChallenges])}</span>
            </div>
            <div className="bg-green-800 rounded-full h-3">
              <div
                className="bg-white h-3 rounded-full transition-all duration-300"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Challenges Grid */}
      <div className="grid md:grid-cols-2 gap-6">
        {challenges.map((challenge, index) => {
          const progress = getChallengeProgress(challenge.id);
          const isCompleted = progress && progress.completed;
          const isUnlocked = isNextChallengeUnlocked(index);

          return (
            <div
              key={challenge.id}
              className={`challenge-card p-6 ${
                !isUnlocked ? 'opacity-50' : ''
              }`}
            >
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    isCompleted
                      ? 'bg-green-100 text-green-600'
                      : isUnlocked
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-100 text-gray-400'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : isUnlocked ? (
                      <Play className="h-5 w-5" />
                    ) : (
                      <Lock className="h-5 w-5" />
                    )}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{getTranslatedContent(challenge, 'title')}</h3>
                    <p className="text-sm text-gray-600">{getTranslatedContent(challenge, 'description')}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="bg-excel-green text-white px-2 py-1 rounded text-sm font-medium">
                    {challenge.points} {t('points')}
                  </div>
                  {isCompleted && progress.score && (
                    <div className="text-green-600 text-sm mt-1">
                      {t('earned')}: {progress.score}
                    </div>
                  )}
                </div>
              </div>

              <p className="text-gray-700 mb-4">{getTranslatedContent(challenge, 'instructions')}</p>

              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">
                  {t('challengeOf', [index + 1, totalChallenges])}
                </div>

                {isUnlocked ? (
                  <Link
                    to={`/challenge/${challenge.id}`}
                    className={`px-4 py-2 rounded-md font-medium transition-colors ${
                      isCompleted
                        ? 'bg-green-100 text-green-700 hover:bg-green-200'
                        : 'bg-excel-green text-white hover:bg-green-700'
                    }`}
                  >
                    {isCompleted ? t('review') : t('startChallenge')}
                  </Link>
                ) : (
                  <div className="px-4 py-2 bg-gray-100 text-gray-500 rounded-md font-medium">
                    {t('locked')}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Level Completion */}
      {completedChallenges === totalChallenges && totalChallenges > 0 && (
        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-6 text-center">
          <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-green-900 mb-2">
            {t('congratulationsLevelComplete')}
          </h3>
          <p className="text-green-700 mb-4">
            {t('youveCompletedAllChallenges')} {getTotalPoints()} {t('pointsExclamation')}
          </p>
          <Link
            to="/dashboard"
            className="bg-excel-green text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
          >
            {t('continueToNextLevel')}
          </Link>
        </div>
      )}
    </div>
  );
};

export default Level;
