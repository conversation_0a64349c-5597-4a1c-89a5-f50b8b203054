import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { challengesAPI } from '../services/api';
import ReactSpreadsheetWrapper from '../components/ReactSpreadsheetWrapper.fixed';
import { CheckCircle, XCircle, ArrowLeft, Lightbulb } from 'lucide-react';

const Challenge = () => {
  const { challengeId } = useParams();
  const { isAuthenticated } = useAuth();
  const { t, language } = useLanguage();
  const navigate = useNavigate();

  const [challenge, setChallenge] = useState(null);
  const [userSolution, setUserSolution] = useState({ currentCell: null });
  const [feedback, setFeedback] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [allChallenges, setAllChallenges] = useState([]);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    const fetchData = async () => {
      try {
        const [challengeResponse, allChallengesResponse] = await Promise.all([
          challengesAPI.getChallenge(challengeId),
          challengesAPI.getChallenges()
        ]);

        setChallenge(challengeResponse.data);
        setAllChallenges(allChallengesResponse.data);
      } catch (error) {
        console.error('Error fetching data:', error);
        navigate('/dashboard');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [challengeId, isAuthenticated, navigate]);

  const handleCellChange = useCallback((cellData, value, oldValue) => {
    if (!cellData) return;
    
    setUserSolution(prevSolution => {
      const cellRef = cellData.ref;
      if (!cellRef) return prevSolution;
      
      // Create a new solution object with the updated cell value
      const newSolution = { 
        ...prevSolution, 
        [cellRef]: value
      };
      
      // Preserve cell formatting if it exists
      if (cellData.format) {
        newSolution[`${cellRef}_format`] = cellData.format;
      }
      
      return newSolution;
    });
  }, []);

  const handleCellSelect = useCallback((cellData) => {
    if (!cellData) return;
    
    const cellRef = cellData.ref;
    if (!cellRef) return;
    
    setUserSolution(prevSolution => ({
      ...prevSolution,
      currentCell: cellRef,
      navigatedCell: null // Clear keyboard navigation state when explicitly selecting
    }));
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!e.key.startsWith('Arrow')) return;
      
      // Get the current selection from the DOM
      const selectedCell = document.querySelector('.fortune-cell-active');
      if (!selectedCell) return;
      
      // Get the cell's position from its ID (format: 'luckysheet-{r}-{c}')
      const cellId = selectedCell.id;
      const match = cellId.match(/luckysheet-(\d+)-(\d+)/);
      if (!match) return;
      
      const row = parseInt(match[1], 10);
      const col = parseInt(match[2], 10);
      
      // Convert to Excel-style reference (e.g., A1, B2)
      const colLetter = String.fromCharCode(65 + col);
      const cellRef = `${colLetter}${row + 1}`;
      
      setUserSolution(prev => ({
        ...prev,
        currentCell: cellRef,
        navigatedCell: cellRef
      }));
    };

    window.addEventListener('keyup', handleKeyDown);
    return () => {
      window.removeEventListener('keyup', handleKeyDown);
    };
  }, []);

  // Local validation function
  const validateSolution = (challenge, userSolution) => {
    const normalizeValue = (value) => {
      if (value === null || value === undefined) return '';
      return String(value).trim().toUpperCase();
    };

    const expected = challenge.solutionData || {};
    let userCell = (userSolution.currentCell || '').toUpperCase();
    const expectedCell = (expected.targetCell || '').toUpperCase();

    // Try to get the current selection from the DOM as a fallback
    if (!userCell) {
      try {
        const selectedCell = document.querySelector('.fortune-cell-active');
        if (selectedCell) {
          const cellId = selectedCell.id;
          const match = cellId.match(/luckysheet-(\d+)-(\d+)/);
          if (match) {
            const row = parseInt(match[1], 10) + 1; // Convert to 1-based row
            const col = parseInt(match[2], 10);
            const colLetter = String.fromCharCode(65 + col);
            userCell = `${colLetter}${row}`;
          }
        }
      } catch (e) {
        console.warn('Error getting selection for validation:', e);
      }
    }

    console.log('Validating solution:', {
      userSolution,
      expected,
      userCell,
      expectedCell,
      currentTime: new Date().toISOString()
    });

    switch (challenge.id) {
      case '1-1': // Navigate interface - check selected cell
        console.log('Validating cell selection:', { userCell, expectedCell, isMatch: userCell === expectedCell });
        return userCell === expectedCell;

      case '1-2': // Enter data - check final result
        return normalizeValue(userSolution.A1) === normalizeValue(expected.cellA1);

      case '1-3': // Format cell - check for bold and yellow background
        // Check if the cell has any formatting data
        if (!userSolution['A1_format']) return false;
        
        // Extract style properties
        const style = userSolution['A1_format'];
        return style.bold === true && 
               (style.backgroundColor === 'yellow' || 
                style.backgroundColor === '#ffff00');

      case '1-4': // Simple calculation
        const userA1 = normalizeValue(userSolution.A1);
        const userB1 = normalizeValue(userSolution.B1);
        const userC1 = normalizeValue(userSolution.C1);

        // Validate numeric values
        const valuesValid = 
          Number(userA1) === Number(expected.cellA1) && 
          Number(userB1) === Number(expected.cellB1);

        // Validate formula or result
        const formulaRegex = /^\s*=\s*[aA]1\s*\+\s*[bB]1\s*$/;
        const resultValid = 
          userC1 === expected.cellC1 || 
          formulaRegex.test(userC1) || 
          Number(userC1) === 30; // 10+20 result

        return valuesValid && resultValid;

      // Level 2 challenges
      case '2-1': // SUM function
        const a6Value = normalizeValue(userSolution.A6);
        // Allow either formula or calculated value
        return a6Value === '15' || 
               a6Value === '=SUM(A1:A5)' || 
               a6Value === '=SUM(A1:A5)';

      case '2-2': // AVERAGE function
        const b6Value = normalizeValue(userSolution.B6);
        return b6Value === '3' || 
               b6Value === '=AVERAGE(A1:A5)' || 
               b6Value === '=AVERAGE(A1:A5)';

      case '2-3': // IF function
        const b1Value = normalizeValue(userSolution.B1);
        // Allow variations in capitalization and spacing
        return b1Value === 'High' || 
               b1Value === 'Low' || 
               b1Value.includes('IF') || 
               /IF\s*\(\s*A1\s*>\s*10\s*,\s*["']?High["']?\s*,\s*["']?Low["']?\s*\)/i.test(b1Value);

      case '2-4': // COUNT function
        const b10Value = normalizeValue(userSolution.B10);
        return b10Value === '10' || 
               b10Value === '=COUNT(A1:A10)' || 
               b10Value === '=COUNT(A1:A10)';

      default:
        return false;
    }
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    setFeedback(null);
    
    // Get the current selection from the DOM as a fallback
    let currentCell = userSolution.currentCell;
    
    try {
      // Get the currently selected cell from the DOM
      const selectedCell = document.querySelector('.fortune-cell-active');
      if (selectedCell) {
        const cellId = selectedCell.id;
        const match = cellId.match(/luckysheet-(\d+)-(\d+)/);
        if (match) {
          const row = parseInt(match[1], 10);
          const col = parseInt(match[2], 10);
          const colLetter = String.fromCharCode(65 + col);
          currentCell = `${colLetter}${row + 1}`;
        }
      }
      
      // Update the user solution with the current selection
      const submissionData = {
        ...userSolution,
        currentCell: currentCell || userSolution.currentCell
      };

      // Try backend validation first, fall back to local validation
      let result;
      try {
        const response = await challengesAPI.validateSolution(challengeId, submissionData);
        result = response.data;
      } catch (backendError) {
        console.log('Backend not available, using local validation');
        // Use local validation
        const isCorrect = validateSolution(challenge, submissionData);
        const score = isCorrect ? challenge.points : 0;
        result = {
          correct: isCorrect,
          message: isCorrect ? t('correctSolution') : t('notCorrect'),
          score: score
        };
      }

      setFeedback(result);
    } catch (error) {
      console.error('Error validating solution:', error);
      setFeedback({
        correct: false,
        message: t('submitError'),
        score: 0
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleReset = () => {
    setUserSolution({});
    setFeedback(null);
  };

  // Memoize the next challenge calculation to prevent unnecessary re-renders
  const nextChallenge = useMemo(() => {
    if (!allChallenges.length) return null;

    const currentIndex = allChallenges.findIndex(c => c.id === challengeId);
    if (currentIndex === -1 || currentIndex === allChallenges.length - 1) {
      return null; // No next challenge
    }

    return allChallenges[currentIndex + 1];
  }, [allChallenges, challengeId]);

  const getNextChallenge = () => nextChallenge;

  // Memoize button texts to prevent re-renders
  const continueButtonText = useMemo(() => {
    return nextChallenge ? t('nextChallenge') : t('backToDashboard');
  }, [nextChallenge, t]);

  const submitButtonText = useMemo(() => {
    return submitting ? t('checkingSolution') : t('submitSolution');
  }, [submitting, t]);

  const handleContinueLearning = () => {
    const nextChallenge = getNextChallenge();
    if (nextChallenge) {
      navigate(`/challenge/${nextChallenge.id}`);
    } else {
      // If no next challenge, go back to the level page
      if (challenge && challenge.levelId) {
        navigate(`/level/${challenge.levelId}`);
      } else {
        navigate('/dashboard');
      }
    }
  };

  const handleBackToLevel = () => {
    if (challenge && challenge.levelId) {
      navigate(`/level/${challenge.levelId}`);
    } else {
      navigate('/dashboard');
    }
  };

  // Helper function to get translated content
  const getTranslatedContent = (item, field) => {
    // For instructions, check if there's a language-specific field
    if (field === 'instructions') {
      if (language === 'en' && item.instructionsEn) {
        return item.instructionsEn;
      } else if (language === 'zh' && item.instructions) {
        return item.instructions;
      }
    }

    const key = `challenge_${item.id}_${field}`;
    const translated = t(key);
    // If translation exists and is different from the key, use it; otherwise use original
    return translated !== key ? translated : item[field];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-excel-green"></div>
      </div>
    );
  }

  if (!challenge) {
    return (
      <div className="text-center py-16">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">{t('challengeNotFound')}</h2>
        <button
          onClick={handleBackToLevel}
          className="bg-excel-green text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
        >
          {t('backToDashboard')}
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <button
          onClick={handleBackToLevel}
          className="flex items-center space-x-2 text-excel-green hover:text-green-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{challenge && challenge.levelId ? t('backToLevel') : t('backToDashboard')}</span>
        </button>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{getTranslatedContent(challenge, 'title')}</h1>
              <p className="text-gray-600 mb-4">{getTranslatedContent(challenge, 'description')}</p>
            </div>
            <div className="text-right">
              <div className="bg-excel-green text-white px-3 py-1 rounded-full text-sm font-medium">
                {challenge.points} {t('points')}
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div className="flex items-start space-x-2">
              <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">{t('instructions')}:</h3>
                <div className="text-blue-800 mb-3 whitespace-pre-line">{getTranslatedContent(challenge, 'instructions')}</div>
                {challenge.id === '1-1' && (
                  <div className="text-sm text-blue-700 bg-blue-100 rounded p-2">
                    💡 <strong>{t('tip')}:</strong> {t('navigationTip')}
                  </div>
                )}
                {challenge.id === '1-3' && (
                  <div className="text-sm text-blue-700 bg-blue-100 rounded p-2">
                    💡 <strong>{t('tip')}:</strong> {t('formattingTip')}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Feedback */}
          {feedback && (
            <div className={`border rounded-lg p-4 mb-4 ${
              feedback.correct
                ? 'bg-green-50 border-green-200'
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-start space-x-2">
                {feedback.correct ? (
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                )}
                <div>
                  <p className={`font-semibold ${
                    feedback.correct ? 'text-green-900' : 'text-red-900'
                  }`}>
                    {feedback.message}
                  </p>
                  {feedback.correct && (
                    <p className="text-green-800 mt-1">
                      {t('youEarnedPoints', [feedback.score])}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Excel Simulator */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('excelWorkspace')}</h2>
        <div className="excel-simulator-container" style={{width: '100%', minWidth: '800px'}}>
          <ReactSpreadsheetWrapper
            onCellChange={handleCellChange}
            onCellSelect={handleCellSelect}
            initialData={userSolution}
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center">
        <button
          onClick={handleReset}
          className="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors"
        >
          {t('reset')}
        </button>

        <div className="space-x-4">
          {feedback?.correct && (
            <button
              onClick={handleContinueLearning}
              className="bg-excel-green text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              {continueButtonText}
            </button>
          )}

          <button
            onClick={handleSubmit}
            disabled={submitting || feedback?.correct}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitButtonText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Challenge;
