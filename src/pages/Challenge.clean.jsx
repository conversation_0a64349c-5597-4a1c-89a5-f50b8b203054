import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { challengesAPI } from '../services/api';
import ReactSpreadsheetWrapper from '../components/ReactSpreadsheetWrapper.fixed';
import { CheckCircle, XCircle, ArrowLeft, Lightbulb } from 'lucide-react';

const Challenge = () => {
  const { challengeId } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { t: translate } = useLanguage();
  
  // Simple translation function with fallback
  const t = useCallback((key) => {
    try {
      return translate(key) || key;
    } catch (e) {
      console.warn(`Translation error for key: ${key}`, e);
      return key;
    }
  }, [translate]);
  
  const [challenge, setChallenge] = useState(null);
  const [allChallenges, setAllChallenges] = useState([]);
  const [userSolution, setUserSolution] = useState({});
  const [feedback, setFeedback] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch challenge data
  useEffect(() => {
    let isMounted = true;
    
    const fetchChallenge = async () => {
      try {
        setLoading(true);
        // First get the challenge details
        const challengeRes = await challengesAPI.getChallenge(challengeId);
        
        if (!isMounted) return;
        
        // Then get all challenges for navigation
        let allChallengesData = [];
        try {
          const levelsRes = await challengesAPI.getLevels();
          const levelIds = levelsRes.data.map(level => level.id);
          
          // Fetch challenges for each level
          const challengesPromises = levelIds.map(levelId => 
            challengesAPI.getChallengesByLevel(levelId)
          );
          
          const levelChallenges = await Promise.all(challengesPromises);
          allChallengesData = levelChallenges.flatMap(res => res.data);
        } catch (challengesErr) {
          console.error('Error fetching all challenges:', challengesErr);
          // Continue with just the current challenge if we can't fetch all
          allChallengesData = [challengeRes.data];
        }
        
        if (!isMounted) return;
        
        setChallenge(challengeRes.data);
        setAllChallenges(allChallengesData);
      } catch (err) {
        console.error('Error fetching challenge:', err);
        if (isMounted) {
          setError('Failed to load challenge. Please try again later.');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchChallenge();
    
    return () => {
      isMounted = false;
    };
  }, [challengeId]); // Removed t from dependencies

  // Handle cell changes
  const handleCellChange = useCallback((cellData, value) => {
    if (!cellData?.ref) return;
    
    setUserSolution(prev => ({
      ...prev,
      [cellData.ref]: value
    }));
  }, []);

  // Handle cell selection
  const handleCellSelect = useCallback((cellData) => {
    if (!cellData?.ref) return;
    
    setUserSolution(prev => ({
      ...prev,
      currentCell: cellData.ref,
      navigatedCell: null
    }));
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!e.key.startsWith('Arrow')) return;
      
      const selectedCell = document.querySelector('.fortune-cell-active');
      if (!selectedCell) return;
      
      const cellId = selectedCell.id;
      const match = cellId.match(/luckysheet-(\d+)-(\d+)/);
      if (!match) return;
      
      const row = parseInt(match[1], 10);
      const col = parseInt(match[2], 10);
      const colLetter = String.fromCharCode(65 + col);
      const cellRef = `${colLetter}${row + 1}`;
      
      setUserSolution(prev => ({
        ...prev,
        currentCell: cellRef,
        navigatedCell: cellRef
      }));
    };

    window.addEventListener('keyup', handleKeyDown);
    return () => {
      window.removeEventListener('keyup', handleKeyDown);
    };
  }, []);

  // Validate solution based on challenge ID and expected solution
  const validateSolution = (challenge) => {
    if (!challenge?.solutionData) {
      console.error('No solution data found for challenge');
      return false;
    }
    
    const expected = challenge.solutionData;
    console.log('Validating challenge', challengeId);
    console.log('Expected solution:', JSON.stringify(expected, null, 2));
    
    try {
      // Check if we have expected solution data
      if (!expected) {
        console.error('No expected solution data found');
        return false;
      }

      // Get the Luckysheet instance
      const sheet = window.luckysheet.getSheet();
      if (!sheet || !sheet.celldata) {
        console.error('Unable to access sheet data');
        return false;
      }

      // Challenge 1-1: Check if C3 is selected
      if (challengeId === '1-1') {
        const selectedRange = window.luckysheet.getRange();
        if (!selectedRange) {
          console.log('No cell selected');
          return false;
        }
        
        // C3 is row 2, column 2 (0-based)
        const isCorrect = selectedRange.row[0] === 2 && selectedRange.column[0] === 2;
        console.log('Selected range:', selectedRange, 'Is C3?', isCorrect);
        return isCorrect;
      }
      
      // Challenge 1-2: Check if A1 contains "Hello Excel"
      if (challengeId === '1-2') {
        const cellData = window.luckysheet.getCellValue(0, 0); // A1 is row 0, column 0
        const isCorrect = cellData?.v === 'Hello Excel' || cellData?.m === 'Hello Excel';
        console.log('Cell A1 value:', cellData, 'Expected: Hello Excel', 'Is correct?', isCorrect);
        return isCorrect;
      }
      
      // For other challenges, try to validate based on expected data
      if (expected.cellA1 !== undefined) {
        const cellData = window.luckysheet.getCellValue(0, 0); // A1
        const isCorrect = cellData?.v === expected.cellA1 || cellData?.m === expected.cellA1;
        console.log('Cell A1 value:', cellData, 'Expected:', expected.cellA1, 'Is correct?', isCorrect);
        return isCorrect;
      }
      
      if (expected.targetCell) {
        // For selection-based challenges
        const selectedRange = window.luckysheet.getRange();
        if (!selectedRange) return false;
        
        const [col, row] = expected.targetCell.match(/[A-Z]+|\d+/g);
        const colNum = col.split('').reduce((acc, char) => acc * 26 + (char.charCodeAt(0) - 64), 0) - 1;
        const rowNum = parseInt(row, 10) - 1;
        
        const isSelected = selectedRange.row[0] === rowNum && selectedRange.column[0] === colNum;
        console.log('Selected range:', selectedRange, 'Expected:', {row: rowNum, col: colNum}, 'Is correct?', isSelected);
        return isSelected;
      }
      
      return false;
    } catch (error) {
      console.error('Error during validation:', error);
      return false;
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    setSubmitting(true);
    setFeedback(null);
    
    try {
      // Check if Luckysheet is ready
      if (!window.luckysheet || typeof window.luckysheet.getCellValue !== 'function') {
        setFeedback({
          correct: false,
          message: t('spreadsheetNotReady') || 'Spreadsheet is not ready. Please wait for it to load before submitting.',
          score: 0
        });
        setSubmitting(false);
        return;
      }
      
      // Get cell data for A1 (for challenge 1-2)
      const cellA1 = window.luckysheet.getCellValue(0, 0);
      const a1Value = cellA1?.v !== undefined ? cellA1.v : cellA1?.m;
      
      // Get selected range (for challenge 1-1)
      const selectedRange = window.luckysheet.getRange();
      const selectedCell = selectedRange ? 
        `${String.fromCharCode(65 + selectedRange.column[0])}${selectedRange.row[0] + 1}` : '';
      
      const submissionData = {
        cellA1: a1Value,
        selectedCell: selectedCell,
        timestamp: new Date().toISOString()
      };

      console.log('Submitting solution data:', submissionData);

      // Try backend validation first, fall back to local validation
      let result;
      try {
        const response = await challengesAPI.validateSolution(challengeId, submissionData);
        result = response.data;
      } catch (backendError) {
        console.log('Using local validation');
        const isCorrect = validateSolution(challenge);
        const score = isCorrect ? (challenge.points || 10) : 0;
        result = {
          correct: isCorrect,
          message: isCorrect ? (t('correctSolution') || 'Correct! Well done!') : (t('notCorrect') || 'Not quite right. Try again!'),
          score: score
        };
      }

      setFeedback(result);
    } catch (error) {
      console.error('Error validating solution:', error);
      setFeedback({
        correct: false,
        message: t('submitError'),
        score: 0
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle reset
  const handleReset = () => {
    setUserSolution({});
    setFeedback(null);
  };

  // Get next challenge
  const nextChallenge = useMemo(() => {
    if (!allChallenges.length) return null;
    const currentIndex = allChallenges.findIndex(c => c.id === challengeId);
    return currentIndex >= 0 && currentIndex < allChallenges.length - 1 
      ? allChallenges[currentIndex + 1] 
      : null;
  }, [allChallenges, challengeId]);

  // Navigation handlers
  const handleContinue = () => {
    if (nextChallenge) {
      navigate(`/challenge/${nextChallenge.id}`);
    } else {
      navigate('/dashboard');
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  if (loading) return <div className="p-4">Loading...</div>;
  if (error) return <div className="p-4 text-red-500">{error}</div>;
  if (!challenge) return <div className="p-4">Challenge not found</div>;

  return (
    <div className="container mx-auto p-4 max-w-5xl">
      <div className="mb-6">
        <button 
          onClick={handleBack}
          className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
        >
          <ArrowLeft className="w-5 h-5 mr-1" />
          {t('back')}
        </button>
        
        <h1 className="text-2xl font-bold mb-2">
          {t(`challenge_${challenge.id}_title`) !== `challenge_${challenge.id}_title`
            ? t(`challenge_${challenge.id}_title`)
            : challenge.title}
        </h1>
        <p className="text-gray-700 mb-4">
          {t(`challenge_${challenge.id}_description`) !== `challenge_${challenge.id}_description`
            ? t(`challenge_${challenge.id}_description`)
            : challenge.description}
        </p>
        {/* Instructions Section */}
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6 rounded">
          <h3 className="font-bold text-lg mb-3">📝 {t('instructions') || 'Instructions'}</h3>
          <div className="space-y-3">
            {t(`challenge_${challenge.id}_instructions`) !== `challenge_${challenge.id}_instructions` ? (
              t(`challenge_${challenge.id}_instructions`).split('\n').map((step, index) => (
                <p key={`inst-${index}`} className="text-gray-800">{step}</p>
              ))
            ) : t('language') === 'zh' && challenge.instructions ? (
              challenge.instructions.split('\n').map((step, index) => (
                <p key={`zh-${index}`} className="text-gray-800">{step}</p>
              ))
            ) : challenge.instructionsEn ? (
              challenge.instructionsEn.split('\n').map((step, index) => (
                <p key={`en-${index}`} className="text-gray-800">{step}</p>
              ))
            ) : (
              <p className="text-gray-500 italic">
                {t('noInstructionsAvailable') || 'No instructions available for this challenge.'}
              </p>
            )}
          </div>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-4 mb-6 relative z-10">
        <div className="mb-4">
          <h2 className="text-lg font-semibold mb-2">{t('yourSolution') || 'Your Solution'}</h2>
          <div style={{ height: '500px', width: '100%', position: 'relative', overflow: 'visible' }}>
            <ReactSpreadsheetWrapper
              initialData={userSolution}
              onCellChange={handleCellChange}
              onCellSelect={handleCellSelect}
              readOnly={false}
            />
          </div>
        </div>
        
        <div className="flex justify-between items-center mt-4 relative z-20" style={{ position: 'relative', zIndex: 20 }}>
          <div className="flex-1">
            <button
              onClick={handleReset}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 mr-4"
              disabled={submitting}
              style={{ position: 'relative', zIndex: 30 }}
            >
              Reset
            </button>
          </div>
          
          <div className="flex items-center space-x-4" style={{ position: 'relative', zIndex: 30 }}>
            {feedback && (
              <div 
                className={`flex items-center ${
                  feedback.correct ? 'text-green-600' : 'text-red-600'
                }`}
                style={{ position: 'relative', zIndex: 30 }}
              >
                {feedback.correct ? (
                  <CheckCircle className="w-5 h-5 mr-1" />
                ) : (
                  <XCircle className="w-5 h-5 mr-1" />
                )}
                <span>{feedback.message}</span>
              </div>
            )}
            
            <button
              onClick={handleSubmit}
              disabled={submitting}
              className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 min-w-[100px] text-center"
              style={{ position: 'relative', zIndex: 30 }}
            >
              {submitting ? 'Submitting...' : 'Submit'}
            </button>
          </div>
        </div>
      </div>
      
      {feedback?.correct && (
        <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
          <div className="flex items-center">
            <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
            <p className="font-medium">{t('challengeComplete')}</p>
          </div>
          <div className="mt-4 flex justify-end">
            <button
              onClick={handleContinue}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              {nextChallenge ? t('nextChallenge') : t('backToDashboard')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Challenge;
