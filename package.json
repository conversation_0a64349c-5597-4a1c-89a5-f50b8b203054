{"name": "excel-learning-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@univerjs/core": "^0.7.0", "@univerjs/design": "^0.7.0", "@univerjs/docs": "^0.7.0", "@univerjs/docs-ui": "^0.7.0", "@univerjs/engine-formula": "^0.7.0", "@univerjs/engine-numfmt": "^0.7.0", "@univerjs/engine-render": "^0.7.0", "@univerjs/sheets": "^0.7.0", "@univerjs/sheets-formula": "^0.7.0", "@univerjs/sheets-formula-ui": "^0.7.0", "@univerjs/sheets-numfmt": "^0.7.0", "@univerjs/sheets-numfmt-ui": "^0.7.0", "@univerjs/sheets-ui": "^0.7.0", "@univerjs/ui": "^0.7.0", "@wendellhu/redi": "^0.18.0", "axios": "^1.9.0", "lucide-react": "^0.511.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.1", "rxjs": "^7.8.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.3.0", "vite": "^4.5.0"}}