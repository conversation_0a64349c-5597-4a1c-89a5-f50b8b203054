const fs = require('fs').promises;
const path = require('path');

class Database {
  constructor() {
    this.dataDir = path.join(__dirname, 'data');
    this.usersFile = path.join(this.dataDir, 'users.json');
    this.progressFile = path.join(this.dataDir, 'progress.json');
    this.levelsFile = path.join(this.dataDir, 'levels.json');
    this.challengesFile = path.join(this.dataDir, 'challenges.json');

    this.init();
  }

  async init() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });

      // Initialize files if they don't exist
      await this.initFile(this.usersFile, []);
      await this.initFile(this.progressFile, []);
      await this.initFile(this.levelsFile, this.getDefaultLevels());
      await this.initFile(this.challengesFile, this.getDefaultChallenges());
    } catch (error) {
      console.error('Database initialization error:', error);
    }
  }

  async initFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch {
      await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
    }
  }

  async readFile(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error(`Error reading ${filePath}:`, error);
      return [];
    }
  }

  async writeFile(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error writing ${filePath}:`, error);
    }
  }

  // Users
  async getUsers() {
    return await this.readFile(this.usersFile);
  }

  async addUser(user) {
    const users = await this.getUsers();
    user.id = Date.now().toString();
    user.createdAt = new Date().toISOString();
    users.push(user);
    await this.writeFile(this.usersFile, users);
    return user;
  }

  async getUserByEmail(email) {
    const users = await this.getUsers();
    return users.find(user => user.email === email);
  }

  async getUserById(id) {
    const users = await this.getUsers();
    return users.find(user => user.id === id);
  }

  // Progress
  async getUserProgress(userId) {
    const progress = await this.readFile(this.progressFile);
    return progress.filter(p => p.userId === userId);
  }

  async updateProgress(progressData) {
    const allProgress = await this.readFile(this.progressFile);
    const existingIndex = allProgress.findIndex(
      p => p.userId === progressData.userId &&
          p.levelId === progressData.levelId &&
          p.challengeId === progressData.challengeId
    );

    if (existingIndex >= 0) {
      allProgress[existingIndex] = { ...allProgress[existingIndex], ...progressData };
    } else {
      progressData.id = Date.now().toString();
      progressData.completedAt = new Date().toISOString();
      allProgress.push(progressData);
    }

    await this.writeFile(this.progressFile, allProgress);
    return progressData;
  }

  // Levels and Challenges
  async getLevels() {
    return await this.readFile(this.levelsFile);
  }

  async getChallenges() {
    return await this.readFile(this.challengesFile);
  }

  async getChallengesByLevel(levelId) {
    const challenges = await this.getChallenges();
    return challenges.filter(c => c.levelId === levelId);
  }

  getDefaultLevels() {
    return [
      {
        id: '1',
        title: 'Excel Basics',
        description: 'Learn the fundamentals of Excel',
        difficulty: 'Beginner',
        orderIndex: 1,
        unlockRequirements: null
      },
      {
        id: '2',
        title: 'Formulas & Functions',
        description: 'Master Excel formulas and basic functions',
        difficulty: 'Beginner',
        orderIndex: 2,
        unlockRequirements: { completedLevel: '1' }
      },
      {
        id: '3',
        title: 'Data Management',
        description: 'Learn to organize and manipulate data',
        difficulty: 'Intermediate',
        orderIndex: 3,
        unlockRequirements: { completedLevel: '2' }
      },
      {
        id: '4',
        title: 'Advanced Functions',
        description: 'Master complex Excel functions',
        difficulty: 'Advanced',
        orderIndex: 4,
        unlockRequirements: { completedLevel: '3' }
      },
      {
        id: '5',
        title: 'Data Analysis',
        description: 'Advanced data analysis and visualization',
        difficulty: 'Expert',
        orderIndex: 5,
        unlockRequirements: { completedLevel: '4' }
      }
    ];
  }

  getDefaultChallenges() {
    return [
      // Level 1: Excel Basics
      {
        id: '1-1',
        levelId: '1',
        title: 'Navigate the Interface',
        description: 'Learn to navigate Excel\'s interface and understand basic components',
        instructions: 'Click on cell A1, then navigate to cell C3 using arrow keys',
        points: 10,
        orderIndex: 1,
        solutionData: { targetCell: 'C3' }
      },
      {
        id: '1-2',
        levelId: '1',
        title: 'Enter and Edit Data',
        description: 'Practice entering and editing data in cells',
        instructions: 'Enter "Hello World" in cell A1, then edit it to "Hello Excel"',
        points: 15,
        orderIndex: 2,
        solutionData: { cellA1: 'Hello Excel' }
      },
      {
        id: '1-3',
        levelId: '1',
        title: 'Basic Cell Formatting',
        description: 'Learn to format cells with bold, italic, and colors',
        instructions: 'Make cell A1 bold and change the background color to yellow',
        points: 20,
        orderIndex: 3,
        solutionData: { cellA1: { bold: true, backgroundColor: 'yellow' } }
      },
      {
        id: '1-4',
        levelId: '1',
        title: 'Simple Calculations',
        description: 'Perform basic arithmetic operations',
        instructions: 'In cell A1 enter 10, in B1 enter 20, in C1 create a formula to add them',
        points: 25,
        orderIndex: 4,
        solutionData: { cellA1: 10, cellB1: 20, cellC1: '=A1+B1' }
      },

      // Level 2: Formulas & Functions
      {
        id: '2-1',
        levelId: '2',
        title: 'SUM Function',
        description: 'Learn to use the SUM function',
        instructions: 'Enter numbers 1-5 in cells A1:A5, then use SUM function in A6',
        points: 20,
        orderIndex: 1,
        solutionData: { cellA1: 1, cellA2: 2, cellA3: 3, cellA4: 4, cellA5: 5, cellA6: '=SUM(A1:A5)' }
      },
      {
        id: '2-2',
        levelId: '2',
        title: 'AVERAGE Function',
        description: 'Calculate averages using the AVERAGE function',
        instructions: 'Use the AVERAGE function to find the average of numbers in A1:A5',
        points: 20,
        orderIndex: 2,
        solutionData: { cellB6: '=AVERAGE(A1:A5)' }
      },
      {
        id: '2-3',
        levelId: '2',
        title: 'IF Function',
        description: 'Learn conditional logic with IF function',
        instructions: 'In B1, create an IF formula: if A1>10, show "High", otherwise "Low"',
        points: 30,
        orderIndex: 3,
        solutionData: { cellB1: '=IF(A1>10,"High","Low")' }
      },
      {
        id: '2-4',
        levelId: '2',
        title: 'COUNT Function',
        description: 'Count cells with numbers using COUNT function',
        instructions: 'Use COUNT function to count numeric values in range A1:A10',
        points: 25,
        orderIndex: 4,
        solutionData: { cellB10: '=COUNT(A1:A10)' }
      }
    ];
  }
}

module.exports = new Database();
