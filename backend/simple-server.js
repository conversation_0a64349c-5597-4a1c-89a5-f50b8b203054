const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Simple validation function
const validateSolution = (challenge, userSolution) => {
  const normalizeValue = (value) => {
    if (value === null || value === undefined) return '';
    return String(value).trim();
  };

  const expected = challenge.solutionData;
  
  switch (challenge.id) {
    case '1-4': // Simple calculation
      const a1Valid = normalizeValue(userSolution.A1) == expected.cellA1;
      const b1Valid = normalizeValue(userSolution.B1) == expected.cellB1;
      const c1Valid = normalizeValue(userSolution.C1) === expected.cellC1 ||
                      normalizeValue(userSolution.C1) === '=A1+B1' ||
                      normalizeValue(userSolution.C1) == 30; // Result of 10+20

      console.log('1-4 validation details:');
      console.log('A1:', normalizeValue(userSolution.A1), 'expected:', expected.cellA1, 'valid:', a1Valid);
      console.log('B1:', normalizeValue(userSolution.B1), 'expected:', expected.cellB1, 'valid:', b1Valid);
      console.log('C1:', normalizeValue(userSolution.C1), 'expected:', expected.cellC1, 'valid:', c1Valid);
      console.log('User solution:', JSON.stringify(userSolution, null, 2));
      console.log('Expected:', JSON.stringify(expected, null, 2));

      return a1Valid && b1Valid && c1Valid;

    default:
      return false;
  }
};

// Mock challenge data
const mockChallenge = {
  id: '1-4',
  solutionData: {
    cellA1: 10,
    cellB1: 20,
    cellC1: '=A1+B1'
  }
};

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Simple server running' });
});

// Validate challenge solution
app.post('/api/challenges/:challengeId/validate', (req, res) => {
  try {
    const { challengeId } = req.params;
    const { userSolution } = req.body;

    console.log(`Validating challenge ${challengeId}`);
    console.log('User solution:', JSON.stringify(userSolution, null, 2));

    if (challengeId !== '1-4') {
      return res.status(404).json({ error: 'Challenge not found' });
    }

    const isCorrect = validateSolution(mockChallenge, userSolution);
    const score = isCorrect ? 25 : 0;

    console.log('Validation result:', isCorrect);

    res.json({
      success: true,
      correct: isCorrect,
      score: score,
      message: isCorrect ? 'Correct! Well done!' : 'Not quite right. Try again!'
    });
  } catch (error) {
    console.error('Validation error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 Simple server running on port ${PORT}`);
  console.log(`📚 Ready for testing!`);
});
