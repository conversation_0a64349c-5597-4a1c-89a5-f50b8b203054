const express = require('express');
const db = require('../database/db');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get user progress
router.get('/', authenticateToken, async (req, res) => {
  try {
    const progress = await db.getUserProgress(req.user.id);
    
    // Calculate stats
    const totalChallenges = (await db.getChallenges()).length;
    const completedChallenges = progress.filter(p => p.completed).length;
    const totalPoints = progress.reduce((sum, p) => sum + (p.score || 0), 0);
    
    // Get current level
    const levels = await db.getLevels();
    let currentLevel = levels[0];
    
    for (const level of levels) {
      const levelChallenges = await db.getChallengesByLevel(level.id);
      const levelProgress = progress.filter(p => p.levelId === level.id && p.completed);
      
      if (levelProgress.length === levelChallenges.length) {
        currentLevel = levels.find(l => l.orderIndex === level.orderIndex + 1) || level;
      } else {
        break;
      }
    }

    res.json({
      progress,
      stats: {
        totalPoints,
        completedChallenges,
        totalChallenges,
        currentLevel: currentLevel.id,
        completionPercentage: Math.round((completedChallenges / totalChallenges) * 100)
      }
    });
  } catch (error) {
    console.error('Get progress error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update challenge progress
router.post('/challenge', authenticateToken, async (req, res) => {
  try {
    const { levelId, challengeId, completed, score, attempts, solutionData } = req.body;

    if (!levelId || !challengeId) {
      return res.status(400).json({ error: 'Level ID and Challenge ID are required' });
    }

    const progressData = {
      userId: req.user.id,
      levelId,
      challengeId,
      completed: completed || false,
      score: score || 0,
      attempts: attempts || 1,
      solutionData: solutionData || {},
      lastAttempt: new Date().toISOString()
    };

    const updatedProgress = await db.updateProgress(progressData);

    res.json({
      message: 'Progress updated successfully',
      progress: updatedProgress
    });
  } catch (error) {
    console.error('Update progress error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get leaderboard
router.get('/leaderboard', async (req, res) => {
  try {
    const users = await db.getUsers();
    const leaderboard = [];

    for (const user of users) {
      const progress = await db.getUserProgress(user.id);
      const totalPoints = progress.reduce((sum, p) => sum + (p.score || 0), 0);
      const completedChallenges = progress.filter(p => p.completed).length;

      leaderboard.push({
        username: user.username,
        totalPoints,
        completedChallenges,
        joinDate: user.createdAt
      });
    }

    // Sort by points, then by completed challenges
    leaderboard.sort((a, b) => {
      if (b.totalPoints !== a.totalPoints) {
        return b.totalPoints - a.totalPoints;
      }
      return b.completedChallenges - a.completedChallenges;
    });

    res.json(leaderboard.slice(0, 10)); // Top 10
  } catch (error) {
    console.error('Get leaderboard error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
