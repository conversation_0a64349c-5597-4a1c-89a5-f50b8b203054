const express = require('express');
const db = require('../database/db');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get all levels
router.get('/levels', async (req, res) => {
  try {
    const levels = await db.getLevels();
    res.json(levels);
  } catch (error) {
    console.error('Get levels error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get challenges for a specific level
router.get('/levels/:levelId/challenges', async (req, res) => {
  try {
    const { levelId } = req.params;
    const challenges = await db.getChallengesByLevel(levelId);

    // Sort by order index
    challenges.sort((a, b) => a.orderIndex - b.orderIndex);

    res.json(challenges);
  } catch (error) {
    console.error('Get challenges error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get specific challenge (must come before / route)
router.get('/:challengeId', async (req, res) => {
  try {
    const { challengeId } = req.params;
    const challenges = await db.getChallenges();
    const challenge = challenges.find(c => c.id === challengeId);

    if (!challenge) {
      return res.status(404).json({ error: 'Challenge not found' });
    }

    res.json(challenge);
  } catch (error) {
    console.error('Get challenge error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all challenges
router.get('/', async (req, res) => {
  console.log('GET / route hit (all challenges)');
  try {
    const challenges = await db.getChallenges();
    console.log('Found challenges:', challenges.length);
    res.json(challenges);
  } catch (error) {
    console.error('Get all challenges error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Validate challenge solution
router.post('/:challengeId/validate', authenticateToken, async (req, res) => {
  try {
    const { challengeId } = req.params;
    const { userSolution } = req.body;

    console.log(`Validating challenge ${challengeId}`);
    console.log('User solution:', JSON.stringify(userSolution, null, 2));

    const challenges = await db.getChallenges();
    const challenge = challenges.find(c => c.id === challengeId);

    if (!challenge) {
      return res.status(404).json({ error: 'Challenge not found' });
    }

    console.log('Expected solution:', JSON.stringify(challenge.solutionData, null, 2));

    // Simple validation logic - this would be more sophisticated in a real app
    const isCorrect = validateSolution(challenge, userSolution);
    const score = isCorrect ? challenge.points : 0;

    // Update progress
    if (isCorrect) {
      await db.updateProgress({
        userId: req.user.id,
        levelId: challenge.levelId,
        challengeId: challenge.id,
        completed: true,
        score: score,
        solutionData: userSolution
      });
    }

    res.json({
      correct: isCorrect,
      score: score,
      message: isCorrect ? 'Congratulations! Challenge completed!' : 'Not quite right. Try again!',
      expectedSolution: isCorrect ? null : challenge.solutionData
    });
  } catch (error) {
    console.error('Validate solution error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Helper function to validate solutions
function validateSolution(challenge, userSolution) {
  const expected = challenge.solutionData;

  // Helper function to convert values for comparison
  const normalizeValue = (value) => {
    if (typeof value === 'string') {
      return value.trim();
    }
    if (typeof value === 'number') {
      return Number(value);
    }
    return value;
  };

  // Basic validation logic - would be more sophisticated in production
  switch (challenge.id) {
    case '1-1': // Navigate to cell
      return userSolution.currentCell === expected.targetCell;

    case '1-2': // Enter data - just check final result
      return normalizeValue(userSolution.A1) === expected.cellA1;

    case '1-3': // Format cell - check for bold and yellow background
      const formatData = userSolution['A1_format'];
      return formatData && formatData.bold === true && formatData.backgroundColor === 'yellow';

    case '1-4': // Simple calculation
      const a1Valid = normalizeValue(userSolution.A1) === expected.cellA1;
      const b1Valid = normalizeValue(userSolution.B1) === expected.cellB1;
      const c1Valid = normalizeValue(userSolution.C1) === expected.cellC1 ||
                      normalizeValue(userSolution.C1) === '=A1+B1' ||
                      normalizeValue(userSolution.C1) === 30; // Result of 10+20

      console.log('1-4 validation details:');
      console.log('A1:', normalizeValue(userSolution.A1), 'expected:', expected.cellA1, 'valid:', a1Valid);
      console.log('B1:', normalizeValue(userSolution.B1), 'expected:', expected.cellB1, 'valid:', b1Valid);
      console.log('C1:', normalizeValue(userSolution.C1), 'expected:', expected.cellC1, 'valid:', c1Valid);

      return a1Valid && b1Valid && c1Valid;

    // Level 2 challenges
    case '2-1': // SUM function
      const sumValid = normalizeValue(userSolution.A1) === expected.cellA1 &&
                       normalizeValue(userSolution.A2) === expected.cellA2 &&
                       normalizeValue(userSolution.A3) === expected.cellA3 &&
                       normalizeValue(userSolution.A4) === expected.cellA4 &&
                       normalizeValue(userSolution.A5) === expected.cellA5 &&
                       (normalizeValue(userSolution.A6) === expected.cellA6 ||
                        normalizeValue(userSolution.A6) === '=SUM(A1:A5)' ||
                        normalizeValue(userSolution.A6) === 15); // Result of 1+2+3+4+5
      return sumValid;

    case '2-2': // AVERAGE function
      return normalizeValue(userSolution.B6) === expected.cellB6 ||
             normalizeValue(userSolution.B6) === '=AVERAGE(A1:A5)' ||
             normalizeValue(userSolution.B6) === 3; // Result of (1+2+3+4+5)/5

    case '2-3': // IF function
      return normalizeValue(userSolution.B1) === expected.cellB1 ||
             normalizeValue(userSolution.B1) === '=IF(A1>10,"High","Low")';

    case '2-4': // COUNT function
      return normalizeValue(userSolution.B10) === expected.cellB10 ||
             normalizeValue(userSolution.B10) === '=COUNT(A1:A10)';

    default:
      return false;
  }
}

// Get user's challenge progress for a specific level
router.get('/levels/:levelId/progress', authenticateToken, async (req, res) => {
  try {
    const { levelId } = req.params;
    const userProgress = await db.getUserProgress(req.user.id);
    const levelProgress = userProgress.filter(p => p.levelId === levelId);

    res.json(levelProgress);
  } catch (error) {
    console.error('Get level progress error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
