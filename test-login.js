import axios from 'axios';

const API_BASE = 'http://localhost:3001/api';

async function testLogin() {
  console.log('🧪 Testing Login Functionality\n');

  // Test data from users.json
  const testUsers = [
    { email: '<EMAIL>', password: 'password123', username: 'demo_user' },
    { email: '<EMAIL>', password: 'password123', username: 'demo' },
    { email: '<EMAIL>', password: 'password123', username: '<PERSON><PERSON><PERSON>' }
  ];

  for (const user of testUsers) {
    console.log(`Testing login for: ${user.email}`);
    
    try {
      const response = await axios.post(`${API_BASE}/auth/login`, {
        email: user.email,
        password: user.password
      });

      if (response.status === 200) {
        console.log(`✅ Login successful for ${user.email}`);
        console.log(`   Username: ${response.data.user.username}`);
        console.log(`   Token: ${response.data.token.substring(0, 20)}...`);
      }
    } catch (error) {
      if (error.response) {
        console.log(`❌ Login failed for ${user.email}: ${error.response.data.error}`);
        console.log(`   Status: ${error.response.status}`);
      } else {
        console.log(`❌ Network error for ${user.email}: ${error.message}`);
      }
    }
    console.log('');
  }

  // Test health endpoint
  console.log('Testing API health...');
  try {
    const healthResponse = await axios.get(`${API_BASE}/health`);
    console.log(`✅ API Health: ${healthResponse.data.message}`);
  } catch (error) {
    console.log(`❌ API Health check failed: ${error.message}`);
  }
}

testLogin().catch(console.error);
