# Excel Learning Platform 📊

A gamified online Excel learning platform that teaches Excel skills through interactive challenges and hands-on practice.

## 🎯 Features

### Core Functionality
- **Interactive Excel Simulator**: Practice Excel operations in a web-based spreadsheet interface
- **Gamified Learning**: Progress through levels and unlock new challenges
- **Progress Tracking**: Monitor learning journey with detailed statistics
- **User Authentication**: Secure login and registration system
- **Leaderboard**: Compete with other learners

### Learning Content
- **Level 1: Excel Basics**
  - Interface navigation
  - Data entry and editing
  - Basic cell formatting
  - Simple calculations

- **Level 2: Formulas & Functions**
  - SUM, AVERAGE, COUNT functions
  - IF statements for conditional logic
  - Formula creation and validation

- **Future Levels** (Framework ready for expansion):
  - Level 3: Data Management
  - Level 4: Advanced Functions
  - Level 5: Data Analysis

## 🛠️ Technology Stack

### Frontend
- **React 19** with Vite for fast development
- **Tailwind CSS** for responsive styling
- **React Router** for navigation
- **Axios** for API communication
- **Lucide React** for icons

### Backend
- **Node.js** with Express framework
- **JSON-based database** (easily replaceable with SQL)
- **JWT authentication** for secure sessions
- **bcryptjs** for password hashing
- **CORS** enabled for cross-origin requests

### Database Schema
- **Users**: Authentication and profile data
- **Levels**: Learning progression structure
- **Challenges**: Individual exercises and solutions
- **Progress**: User completion tracking and scores

## 🚀 Getting Started

### Prerequisites
- Node.js (v18+ recommended, currently running on v17.7.1)
- npm or yarn package manager

### Installation

1. **Clone and navigate to the project**
   ```bash
   cd excel-learning-platform
   ```

2. **Install frontend dependencies**
   ```bash
   npm install
   ```

3. **Install backend dependencies**
   ```bash
   cd backend
   npm install
   cd ..
   ```

### Running the Application

1. **Start the backend server**
   ```bash
   cd backend
   node server.js
   ```
   Backend runs on: http://localhost:3001

2. **Start the frontend development server**
   ```bash
   # In a new terminal, from project root
   node node_modules/.bin/vite
   ```
   Frontend runs on: http://localhost:5174

### API Endpoints

- `GET /api/health` - Health check
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `GET /api/challenges/levels` - Get all levels
- `GET /api/challenges/levels/:levelId/challenges` - Get challenges for level
- `POST /api/challenges/challenges/:challengeId/validate` - Validate solution
- `GET /api/progress` - Get user progress
- `POST /api/progress/challenge` - Update challenge progress
- `GET /api/progress/leaderboard` - Get leaderboard

## 🎮 How to Use

1. **Register/Login**: Create an account or sign in
2. **Dashboard**: View your progress and available levels
3. **Select Level**: Choose an unlocked level to start learning
4. **Complete Challenges**: Work through exercises in the Excel simulator
5. **Track Progress**: Monitor your advancement and points earned
6. **Compete**: Check the leaderboard to see how you rank

## 🏗️ Architecture

### Frontend Structure
```
src/
├── components/          # Reusable UI components
│   ├── ExcelSimulator.jsx
│   └── Navbar.jsx
├── contexts/           # React contexts
│   └── AuthContext.jsx
├── pages/              # Route components
│   ├── Home.jsx
│   ├── Dashboard.jsx
│   ├── Level.jsx
│   ├── Challenge.jsx
│   └── Leaderboard.jsx
├── services/           # API communication
│   └── api.js
└── App.jsx            # Main application component
```

### Backend Structure
```
backend/
├── database/          # Data layer
│   ├── db.js         # Database operations
│   └── data/         # JSON data files
├── middleware/        # Express middleware
│   └── auth.js       # JWT authentication
├── routes/           # API routes
│   ├── auth.js       # Authentication endpoints
│   ├── challenges.js # Challenge management
│   └── progress.js   # Progress tracking
└── server.js         # Express server setup
```

## 🔧 Customization

### Adding New Challenges
1. Edit `backend/database/db.js`
2. Add challenges to `getDefaultChallenges()` method
3. Update validation logic in `backend/routes/challenges.js`
4. Restart the backend server

### Adding New Levels
1. Edit `backend/database/db.js`
2. Add levels to `getDefaultLevels()` method
3. Create corresponding challenges
4. Restart the backend server

## 🚀 Deployment Considerations

- **Database**: Replace JSON files with PostgreSQL/MySQL for production
- **Authentication**: Add password reset, email verification
- **File Storage**: Use cloud storage for user uploads
- **Caching**: Implement Redis for session management
- **Security**: Add rate limiting, input validation
- **Monitoring**: Add logging and error tracking

## 📝 License

This project is created for educational purposes. Feel free to use and modify as needed.

## 🤝 Contributing

This is a demonstration project. For production use, consider:
- Adding comprehensive testing
- Implementing proper error handling
- Adding data validation
- Enhancing security measures
- Optimizing performance
