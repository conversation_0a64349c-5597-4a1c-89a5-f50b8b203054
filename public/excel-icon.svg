<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <!-- Excel Green Background -->
  <rect width="64" height="64" rx="8" fill="#217346"/>
  
  <!-- White Excel Grid -->
  <g fill="white" opacity="0.9">
    <!-- Grid Lines -->
    <rect x="12" y="16" width="40" height="32" fill="none" stroke="white" stroke-width="1"/>
    
    <!-- Horizontal Lines -->
    <line x1="12" y1="24" x2="52" y2="24" stroke="white" stroke-width="0.5"/>
    <line x1="12" y1="32" x2="52" y2="32" stroke="white" stroke-width="0.5"/>
    <line x1="12" y1="40" x2="52" y2="40" stroke="white" stroke-width="0.5"/>
    
    <!-- Vertical Lines -->
    <line x1="20" y1="16" x2="20" y2="48" stroke="white" stroke-width="0.5"/>
    <line x1="28" y1="16" x2="28" y2="48" stroke="white" stroke-width="0.5"/>
    <line x1="36" y1="16" x2="36" y2="48" stroke="white" stroke-width="0.5"/>
    <line x1="44" y1="16" x2="44" y2="48" stroke="white" stroke-width="0.5"/>
    
    <!-- Excel "X" Symbol -->
    <text x="32" y="36" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">X</text>
  </g>
  
  <!-- Highlight Cell -->
  <rect x="20" y="24" width="8" height="8" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
</svg>
